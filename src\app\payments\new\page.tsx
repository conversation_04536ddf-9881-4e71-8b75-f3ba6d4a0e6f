'use client'

import { useSearchParams } from 'next/navigation'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { PaymentForm } from '@/components/forms/PaymentForm'

function AddPaymentContent() {
  const searchParams = useSearchParams()
  const customerId = searchParams.get('customer_id')
  const debtId = searchParams.get('debt_id')

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Process Payment</h1>
        <p className="text-gray-600 mt-2">Record a customer payment and update debt balances</p>
      </div>

      <PaymentForm 
        preselectedCustomerId={customerId || undefined}
        preselectedDebtId={debtId || undefined}
      />
    </div>
  )
}

export default function AddPaymentPage() {
  return (
    <ProtectedRoute requiredPermission="process_payment">
      <AddPaymentContent />
    </ProtectedRoute>
  )
}
