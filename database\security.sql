-- Row Level Security (RLS) Policies for Sari-Sari Store Admin Dashboard

-- Enable RLS on all tables
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE debt_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_profiles ENABLE ROW LEVEL SECURITY;

-- Helper function to check if user is authenticated admin
CREATE OR REPLACE FUNCTION is_authenticated_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM admin_profiles 
        WHERE id = auth.uid() 
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get user role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT role FROM admin_profiles 
        WHERE id = auth.uid() 
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Categories policies
CREATE POLICY "Admins can view all categories" ON categories
    FOR SELECT USING (is_authenticated_admin());

CREATE POLICY "Admins can insert categories" ON categories
    FOR INSERT WITH CHECK (is_authenticated_admin());

CREATE POLICY "Admins can update categories" ON categories
    FOR UPDATE USING (is_authenticated_admin());

CREATE POLICY "Super admins can delete categories" ON categories
    FOR DELETE USING (get_user_role() = 'super_admin');

-- Products policies
CREATE POLICY "Admins can view all products" ON products
    FOR SELECT USING (is_authenticated_admin());

CREATE POLICY "Admins can insert products" ON products
    FOR INSERT WITH CHECK (is_authenticated_admin());

CREATE POLICY "Admins can update products" ON products
    FOR UPDATE USING (is_authenticated_admin());

CREATE POLICY "Super admins can delete products" ON products
    FOR DELETE USING (get_user_role() = 'super_admin');

-- Customers policies
CREATE POLICY "Admins can view all customers" ON customers
    FOR SELECT USING (is_authenticated_admin());

CREATE POLICY "Admins can insert customers" ON customers
    FOR INSERT WITH CHECK (is_authenticated_admin());

CREATE POLICY "Admins can update customers" ON customers
    FOR UPDATE USING (is_authenticated_admin());

CREATE POLICY "Super admins can delete customers" ON customers
    FOR DELETE USING (get_user_role() = 'super_admin');

-- Debt transactions policies
CREATE POLICY "Admins can view all debt transactions" ON debt_transactions
    FOR SELECT USING (is_authenticated_admin());

CREATE POLICY "Admins can insert debt transactions" ON debt_transactions
    FOR INSERT WITH CHECK (is_authenticated_admin());

CREATE POLICY "Admins can update debt transactions" ON debt_transactions
    FOR UPDATE USING (is_authenticated_admin());

CREATE POLICY "Super admins can delete debt transactions" ON debt_transactions
    FOR DELETE USING (get_user_role() = 'super_admin');

-- Payments policies
CREATE POLICY "Admins can view all payments" ON payments
    FOR SELECT USING (is_authenticated_admin());

CREATE POLICY "Admins can insert payments" ON payments
    FOR INSERT WITH CHECK (is_authenticated_admin());

CREATE POLICY "Admins can update payments" ON payments
    FOR UPDATE USING (is_authenticated_admin());

CREATE POLICY "Super admins can delete payments" ON payments
    FOR DELETE USING (get_user_role() = 'super_admin');

-- Inventory movements policies
CREATE POLICY "Admins can view all inventory movements" ON inventory_movements
    FOR SELECT USING (is_authenticated_admin());

CREATE POLICY "Admins can insert inventory movements" ON inventory_movements
    FOR INSERT WITH CHECK (is_authenticated_admin());

CREATE POLICY "Super admins can update inventory movements" ON inventory_movements
    FOR UPDATE USING (get_user_role() = 'super_admin');

CREATE POLICY "Super admins can delete inventory movements" ON inventory_movements
    FOR DELETE USING (get_user_role() = 'super_admin');

-- Admin profiles policies
CREATE POLICY "Admins can view their own profile" ON admin_profiles
    FOR SELECT USING (id = auth.uid());

CREATE POLICY "Super admins can view all profiles" ON admin_profiles
    FOR SELECT USING (get_user_role() = 'super_admin');

CREATE POLICY "Admins can update their own profile" ON admin_profiles
    FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Super admins can manage all profiles" ON admin_profiles
    FOR ALL USING (get_user_role() = 'super_admin');

-- Function to create admin profile on user signup
CREATE OR REPLACE FUNCTION handle_new_admin_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO admin_profiles (id, full_name, role)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        COALESCE(NEW.raw_user_meta_data->>'role', 'admin')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create admin profile when new user signs up
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_admin_user();

-- Grant necessary permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Grant permissions for the service role (for admin operations)
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO service_role;
