import { v2 as cloudinary } from 'cloudinary'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
})

export { cloudinary }

// Upload image to Cloudinary
export const uploadImage = async (
  file: File,
  folder: string = 'sari-sari-store/products'
): Promise<{
  success: boolean
  url?: string
  public_id?: string
  error?: string
}> => {
  try {
    // Convert file to base64
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const base64 = buffer.toString('base64')
    const dataURI = `data:${file.type};base64,${base64}`

    // Upload to Cloudinary
    const result = await cloudinary.uploader.upload(dataURI, {
      folder,
      resource_type: 'auto',
      transformation: [
        { width: 800, height: 600, crop: 'limit' },
        { quality: 'auto' },
        { format: 'auto' }
      ]
    })

    return {
      success: true,
      url: result.secure_url,
      public_id: result.public_id
    }
  } catch (error) {
    console.error('Cloudinary upload error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    }
  }
}

// Delete image from Cloudinary
export const deleteImage = async (
  public_id: string
): Promise<{
  success: boolean
  error?: string
}> => {
  try {
    await cloudinary.uploader.destroy(public_id)
    return { success: true }
  } catch (error) {
    console.error('Cloudinary delete error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Delete failed'
    }
  }
}

// Get optimized image URL
export const getOptimizedImageUrl = (
  public_id: string,
  options: {
    width?: number
    height?: number
    crop?: string
    quality?: string | number
    format?: string
  } = {}
): string => {
  const {
    width = 400,
    height = 300,
    crop = 'fill',
    quality = 'auto',
    format = 'auto'
  } = options

  return cloudinary.url(public_id, {
    width,
    height,
    crop,
    quality,
    format,
    secure: true
  })
}

// Generate thumbnail URL
export const getThumbnailUrl = (public_id: string): string => {
  return getOptimizedImageUrl(public_id, {
    width: 150,
    height: 150,
    crop: 'fill'
  })
}

// Generate product image URL
export const getProductImageUrl = (public_id: string): string => {
  return getOptimizedImageUrl(public_id, {
    width: 400,
    height: 400,
    crop: 'fill'
  })
}

// Validate image file
export const validateImageFile = (file: File): {
  isValid: boolean
  error?: string
} => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Invalid file type. Please upload JPEG, PNG, or WebP images.'
    }
  }

  // Check file size (max 5MB)
  const maxSize = 5 * 1024 * 1024 // 5MB in bytes
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File size too large. Please upload images smaller than 5MB.'
    }
  }

  return { isValid: true }
}

// Client-side upload using upload preset
export const uploadImageClient = async (
  file: File,
  uploadPreset: string = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET!
): Promise<{
  success: boolean
  url?: string
  public_id?: string
  error?: string
}> => {
  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('upload_preset', uploadPreset)
    formData.append('folder', 'sari-sari-store/products')

    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload`,
      {
        method: 'POST',
        body: formData
      }
    )

    if (!response.ok) {
      throw new Error('Upload failed')
    }

    const result = await response.json()

    return {
      success: true,
      url: result.secure_url,
      public_id: result.public_id
    }
  } catch (error) {
    console.error('Client upload error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    }
  }
}
