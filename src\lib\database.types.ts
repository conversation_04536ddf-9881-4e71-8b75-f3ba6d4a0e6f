// Generated Supabase Database Types
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      admin_profiles: {
        Row: {
          id: string
          full_name: string | null
          role: string
          is_active: boolean
          last_login: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          full_name?: string | null
          role?: string
          is_active?: boolean
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string | null
          role?: string
          is_active?: boolean
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      customers: {
        Row: {
          id: string
          first_name: string
          family_name: string
          full_name: string
          phone: string | null
          address: string | null
          email: string | null
          is_active: boolean
          total_debt: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          first_name: string
          family_name: string
          phone?: string | null
          address?: string | null
          email?: string | null
          is_active?: boolean
          total_debt?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          first_name?: string
          family_name?: string
          phone?: string | null
          address?: string | null
          email?: string | null
          is_active?: boolean
          total_debt?: number
          created_at?: string
          updated_at?: string
        }
      }
      debt_transactions: {
        Row: {
          id: string
          customer_id: string
          product_id: string
          product_name: string
          product_price: number
          quantity: number
          total_amount: number
          remaining_balance: number
          debt_date: string
          is_fully_paid: boolean
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          product_id: string
          product_name: string
          product_price: number
          quantity: number
          remaining_balance: number
          debt_date?: string
          is_fully_paid?: boolean
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          product_id?: string
          product_name?: string
          product_price?: number
          quantity?: number
          remaining_balance?: number
          debt_date?: string
          is_fully_paid?: boolean
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      inventory_movements: {
        Row: {
          id: string
          product_id: string
          movement_type: string
          quantity: number
          previous_stock: number
          new_stock: number
          reason: string | null
          reference_id: string | null
          notes: string | null
          created_at: string
        }
        Insert: {
          id?: string
          product_id: string
          movement_type: string
          quantity: number
          previous_stock: number
          new_stock: number
          reason?: string | null
          reference_id?: string | null
          notes?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          product_id?: string
          movement_type?: string
          quantity?: number
          previous_stock?: number
          new_stock?: number
          reason?: string | null
          reference_id?: string | null
          notes?: string | null
          created_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          customer_id: string
          debt_transaction_id: string | null
          amount_paid: number
          payment_date: string
          payment_method: string
          notes: string | null
          recorded_by: string | null
          created_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          debt_transaction_id?: string | null
          amount_paid: number
          payment_date?: string
          payment_method?: string
          notes?: string | null
          recorded_by?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          debt_transaction_id?: string | null
          amount_paid?: number
          payment_date?: string
          payment_method?: string
          notes?: string | null
          recorded_by?: string | null
          created_at?: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          description: string | null
          image_url: string | null
          image_public_id: string | null
          net_weight: string | null
          price: number
          stock_quantity: number
          category_id: string | null
          barcode: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          image_url?: string | null
          image_public_id?: string | null
          net_weight?: string | null
          price: number
          stock_quantity?: number
          category_id?: string | null
          barcode?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          image_url?: string | null
          image_public_id?: string | null
          net_weight?: string | null
          price?: number
          stock_quantity?: number
          category_id?: string | null
          barcode?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      process_payment: {
        Args: {
          p_customer_id: string
          p_amount_paid: number
          p_payment_method?: string
          p_notes?: string
          p_recorded_by?: string
        }
        Returns: Json
      }
      create_debt_transaction: {
        Args: {
          p_customer_id: string
          p_product_id: string
          p_quantity: number
          p_notes?: string
        }
        Returns: Json
      }
      get_customer_debt_summary: {
        Args: {
          p_customer_id: string
        }
        Returns: Json
      }
      is_authenticated_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      get_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
