{"name": "caparan-tin<PERSON>an", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@supabase/supabase-js": "^2.50.0", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "next": "15.3.4", "next-cloudinary": "^6.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "prettier": "^3.6.0", "tailwindcss": "^4", "typescript": "^5"}}