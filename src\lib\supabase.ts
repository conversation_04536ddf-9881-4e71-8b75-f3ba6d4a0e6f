import { createClient } from '@supabase/supabase-js'
import type { Database } from './database.types'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// For server-side operations
export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Database function helpers
export const dbFunctions = {
  // Process payment for a customer
  processPayment: async (params: {
    customer_id: string
    amount_paid: number
    payment_method?: string
    notes?: string
    recorded_by?: string
  }) => {
    const { data, error } = await supabase.rpc('process_payment', params)
    return { data, error }
  },

  // Create debt transaction
  createDebtTransaction: async (params: {
    customer_id: string
    product_id: string
    quantity: number
    notes?: string
  }) => {
    const { data, error } = await supabase.rpc('create_debt_transaction', params)
    return { data, error }
  },

  // Get customer debt summary
  getCustomerDebtSummary: async (customer_id: string) => {
    const { data, error } = await supabase.rpc('get_customer_debt_summary', {
      p_customer_id: customer_id
    })
    return { data, error }
  }
}

// Helper functions for common queries
export const queries = {
  // Get products with category information
  getProductsWithCategory: () => {
    return supabase
      .from('products')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('is_active', true)
      .order('name')
  },

  // Get customers with debt information
  getCustomersWithDebt: () => {
    return supabase
      .from('customers')
      .select(`
        *,
        debt_transactions(
          id,
          remaining_balance,
          is_fully_paid
        )
      `)
      .eq('is_active', true)
      .order('family_name')
  },

  // Get debt transactions with related data
  getDebtTransactionsWithDetails: () => {
    return supabase
      .from('debt_transactions')
      .select(`
        *,
        customer:customers(*),
        product:products(*)
      `)
      .order('debt_date', { ascending: false })
  },

  // Get payments with related data
  getPaymentsWithDetails: () => {
    return supabase
      .from('payments')
      .select(`
        *,
        customer:customers(*),
        debt_transaction:debt_transactions(*)
      `)
      .order('payment_date', { ascending: false })
  },

  // Get low stock products
  getLowStockProducts: (threshold: number = 10) => {
    return supabase
      .from('products')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('is_active', true)
      .lte('stock_quantity', threshold)
      .order('stock_quantity')
  },

  // Get dashboard statistics
  getDashboardStats: async () => {
    const [
      { count: totalCustomers },
      { count: totalProducts },
      { data: totalDebt },
      { data: todayPayments },
      { count: lowStockCount },
      { count: activeDebts }
    ] = await Promise.all([
      supabase.from('customers').select('*', { count: 'exact', head: true }).eq('is_active', true),
      supabase.from('products').select('*', { count: 'exact', head: true }).eq('is_active', true),
      supabase.from('customers').select('total_debt').eq('is_active', true),
      supabase.from('payments').select('amount_paid').gte('payment_date', new Date().toISOString().split('T')[0]),
      supabase.from('products').select('*', { count: 'exact', head: true }).eq('is_active', true).lte('stock_quantity', 10),
      supabase.from('debt_transactions').select('*', { count: 'exact', head: true }).eq('is_fully_paid', false)
    ])

    const totalDebtAmount = totalDebt?.reduce((sum, customer) => sum + (customer.total_debt || 0), 0) || 0
    const totalPaymentsToday = todayPayments?.reduce((sum, payment) => sum + (payment.amount_paid || 0), 0) || 0

    return {
      total_customers: totalCustomers || 0,
      total_products: totalProducts || 0,
      total_debt_amount: totalDebtAmount,
      total_payments_today: totalPaymentsToday,
      low_stock_products: lowStockCount || 0,
      active_debts: activeDebts || 0
    }
  }
}
