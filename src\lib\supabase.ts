import { createClient } from '@supabase/supabase-js'
import type { Database } from './database.types'
import { config } from './config'

// Get configuration from centralized config module
const { supabase: supabaseConfig } = config

// Create main Supabase client for client-side operations
export const supabase = createClient<Database>(supabaseConfig.url, supabaseConfig.anonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
})

// Create admin client for server-side operations (only if service role key is available)
export const supabaseAdmin = supabaseConfig.serviceRoleKey
  ? createClient<Database>(supabaseConfig.url, supabaseConfig.serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    })
  : null

// Helper function to get admin client with validation
export function getSupabaseAdmin() {
  if (!supabaseAdmin) {
    throw new Error(
      'Supabase admin client not available. ' +
        'SUPABASE_SERVICE_ROLE_KEY environment variable is required for admin operations.'
    )
  }
  return supabaseAdmin
}

// Database function helpers
export const dbFunctions = {
  // Process payment for a customer
  processPayment: async (params: {
    customer_id: string
    amount_paid: number
    payment_method?: string
    notes?: string
    recorded_by?: string
  }) => {
    try {
      const { data, error } = await supabase.rpc('process_payment', params)
      return { data, error }
    } catch (error) {
      console.error('Error processing payment:', error)
      return { data: null, error }
    }
  },

  // Create debt transaction
  createDebtTransaction: async (params: {
    customer_id: string
    product_id: string
    quantity: number
    notes?: string
  }) => {
    try {
      const { data, error } = await supabase.rpc('create_debt_transaction', params)
      return { data, error }
    } catch (error) {
      console.error('Error creating debt transaction:', error)
      return { data: null, error }
    }
  },

  // Get customer debt summary
  getCustomerDebtSummary: async (customer_id: string) => {
    try {
      const { data, error } = await supabase.rpc('get_customer_debt_summary', {
        p_customer_id: customer_id,
      })
      return { data, error }
    } catch (error) {
      console.error('Error getting customer debt summary:', error)
      return { data: null, error }
    }
  },
}

// Helper functions for common queries
export const queries = {
  // Get products with category information
  getProductsWithCategory: () => {
    return supabase
      .from('products')
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .eq('is_active', true)
      .order('name')
  },

  // Get customers with debt information
  getCustomersWithDebt: () => {
    return supabase
      .from('customers')
      .select(
        `
        *,
        debt_transactions(
          id,
          remaining_balance,
          is_fully_paid
        )
      `
      )
      .eq('is_active', true)
      .order('family_name')
  },

  // Get debt transactions with related data
  getDebtTransactionsWithDetails: () => {
    return supabase
      .from('debt_transactions')
      .select(
        `
        *,
        customer:customers(*),
        product:products(*)
      `
      )
      .order('debt_date', { ascending: false })
  },

  // Get payments with related data
  getPaymentsWithDetails: () => {
    return supabase
      .from('payments')
      .select(
        `
        *,
        customer:customers(*),
        debt_transaction:debt_transactions(*)
      `
      )
      .order('payment_date', { ascending: false })
  },

  // Get low stock products
  getLowStockProducts: (threshold: number = 10) => {
    return supabase
      .from('products')
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .eq('is_active', true)
      .lte('stock_quantity', threshold)
      .order('stock_quantity')
  },

  // Get dashboard statistics
  getDashboardStats: async () => {
    const [
      { count: totalCustomers },
      { count: totalProducts },
      { data: totalDebt },
      { data: todayPayments },
      { count: lowStockCount },
      { count: activeDebts },
    ] = await Promise.all([
      supabase.from('customers').select('*', { count: 'exact', head: true }).eq('is_active', true),
      supabase.from('products').select('*', { count: 'exact', head: true }).eq('is_active', true),
      supabase.from('customers').select('total_debt').eq('is_active', true),
      supabase
        .from('payments')
        .select('amount_paid')
        .gte('payment_date', new Date().toISOString().split('T')[0]),
      supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)
        .lte('stock_quantity', 10),
      supabase
        .from('debt_transactions')
        .select('*', { count: 'exact', head: true })
        .eq('is_fully_paid', false),
    ])

    const totalDebtAmount =
      totalDebt?.reduce((sum, customer) => sum + (customer.total_debt || 0), 0) || 0
    const totalPaymentsToday =
      todayPayments?.reduce((sum, payment) => sum + (payment.amount_paid || 0), 0) || 0

    return {
      total_customers: totalCustomers || 0,
      total_products: totalProducts || 0,
      total_debt_amount: totalDebtAmount,
      total_payments_today: totalPaymentsToday,
      low_stock_products: lowStockCount || 0,
      active_debts: activeDebts || 0,
    }
  },
}
