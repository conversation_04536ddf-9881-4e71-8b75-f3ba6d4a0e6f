// Database Types for Sari-Sari Store Admin Dashboard

export interface Category {
  id: string
  name: string
  description?: string
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  name: string
  description?: string
  image_url?: string
  image_public_id?: string
  net_weight?: string
  price: number
  stock_quantity: number
  category_id?: string
  category?: Category
  barcode?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Customer {
  id: string
  first_name: string
  family_name: string
  full_name: string
  phone?: string
  address?: string
  email?: string
  is_active: boolean
  total_debt: number
  created_at: string
  updated_at: string
}

export interface DebtTransaction {
  id: string
  customer_id: string
  customer?: Customer
  product_id: string
  product?: Product
  product_name: string
  product_price: number
  quantity: number
  total_amount: number
  remaining_balance: number
  debt_date: string
  is_fully_paid: boolean
  notes?: string
  created_at: string
  updated_at: string
}

export interface Payment {
  id: string
  customer_id: string
  customer?: Customer
  debt_transaction_id?: string
  debt_transaction?: DebtTransaction
  amount_paid: number
  payment_date: string
  payment_method: string
  notes?: string
  recorded_by?: string
  created_at: string
}

export interface InventoryMovement {
  id: string
  product_id: string
  product?: Product
  movement_type: 'in' | 'out' | 'adjustment'
  quantity: number
  previous_stock: number
  new_stock: number
  reason?: string
  reference_id?: string
  notes?: string
  created_at: string
}

export interface AdminProfile {
  id: string
  full_name?: string
  role: 'super_admin' | 'admin' | 'cashier'
  is_active: boolean
  last_login?: string
  created_at: string
  updated_at: string
}

// API Response Types
export interface ApiResponse<T> {
  data: T
  message?: string
  error?: string
  success: boolean
}

export interface PaginationParams {
  page: number
  limit: number
  search?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form Types
export interface ProductFormData {
  name: string
  description?: string
  net_weight?: string
  price: number
  stock_quantity: number
  category_id?: string
  barcode?: string
  image?: File
}

export interface CustomerFormData {
  first_name: string
  family_name: string
  phone?: string
  address?: string
  email?: string
}

export interface DebtFormData {
  customer_id: string
  product_id: string
  quantity: number
  notes?: string
}

export interface PaymentFormData {
  customer_id: string
  amount_paid: number
  payment_method: string
  notes?: string
  recorded_by?: string
}

// Dashboard Analytics Types
export interface DashboardStats {
  total_customers: number
  total_products: number
  total_debt_amount: number
  total_payments_today: number
  low_stock_products: number
  active_debts: number
}

export interface CustomerDebtSummary {
  customer: Customer
  active_debts: DebtTransaction[]
  recent_payments: Payment[]
  debt_statistics: {
    total_transactions: number
    total_amount_borrowed: number
    total_amount_paid: number
    oldest_debt_date?: string
  }
}

// Filter and Search Types
export interface ProductFilters {
  category_id?: string
  is_active?: boolean
  low_stock?: boolean
  price_min?: number
  price_max?: number
}

export interface CustomerFilters {
  has_debt?: boolean
  is_active?: boolean
  search?: string
}

export interface DebtFilters {
  customer_id?: string
  is_fully_paid?: boolean
  date_from?: string
  date_to?: string
}

export interface PaymentFilters {
  customer_id?: string
  payment_method?: string
  date_from?: string
  date_to?: string
}
