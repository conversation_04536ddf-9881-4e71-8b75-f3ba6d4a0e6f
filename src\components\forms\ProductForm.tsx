'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { ImageUpload } from '@/components/ui/ImageUpload'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { productService, categoryService } from '@/lib/services/productService'
import { deleteImage } from '@/lib/cloudinary'
import type { Product, Category, ProductFormData } from '@/types'

interface ProductFormProps {
  product?: Product
  onSuccess?: (product: Product) => void
  onCancel?: () => void
}

export function ProductForm({ product, onSuccess, onCancel }: ProductFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [error, setError] = useState('')
  
  const [formData, setFormData] = useState<ProductFormData>({
    name: product?.name || '',
    description: product?.description || '',
    net_weight: product?.net_weight || '',
    price: product?.price || 0,
    stock_quantity: product?.stock_quantity || 0,
    category_id: product?.category_id || '',
    barcode: product?.barcode || ''
  })

  const [imageUrl, setImageUrl] = useState(product?.image_url || '')
  const [imagePublicId, setImagePublicId] = useState(product?.image_public_id || '')

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      const data = await categoryService.getCategories()
      setCategories(data)
    } catch (error) {
      console.error('Failed to load categories:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }))
  }

  const handleImageChange = (url: string, publicId: string) => {
    setImageUrl(url)
    setImagePublicId(publicId)
  }

  const handleImageRemove = async () => {
    if (imagePublicId) {
      try {
        await deleteImage(imagePublicId)
      } catch (error) {
        console.error('Failed to delete image:', error)
      }
    }
    setImageUrl('')
    setImagePublicId('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      const productData = {
        ...formData,
        image_url: imageUrl || null,
        image_public_id: imagePublicId || null
      }

      let savedProduct: Product

      if (product) {
        // Update existing product
        savedProduct = await productService.updateProduct(product.id, productData)
      } else {
        // Create new product
        savedProduct = await productService.createProduct(productData)
      }

      if (onSuccess) {
        onSuccess(savedProduct)
      } else {
        router.push('/products')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save product')
      console.error('Product save error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      router.back()
    }
  }

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{product ? 'Edit Product' : 'Add New Product'}</CardTitle>
        <CardDescription>
          {product ? 'Update product information' : 'Enter product details to add to inventory'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Product Image */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Product Image
            </label>
            <ImageUpload
              value={imageUrl}
              onChange={handleImageChange}
              onRemove={handleImageRemove}
              disabled={isLoading}
            />
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Product Name *"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              placeholder="Enter product name"
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                name="category_id"
                value={formData.category_id}
                onChange={handleInputChange}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter product description"
            />
          </div>

          {/* Product Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="Net Weight"
              name="net_weight"
              value={formData.net_weight}
              onChange={handleInputChange}
              placeholder="e.g., 100g, 1L"
            />

            <Input
              label="Price (₱) *"
              name="price"
              type="number"
              step="0.01"
              min="0"
              value={formData.price}
              onChange={handleInputChange}
              required
              placeholder="0.00"
            />

            <Input
              label="Stock Quantity *"
              name="stock_quantity"
              type="number"
              min="0"
              value={formData.stock_quantity}
              onChange={handleInputChange}
              required
              placeholder="0"
            />
          </div>

          <Input
            label="Barcode"
            name="barcode"
            value={formData.barcode}
            onChange={handleInputChange}
            placeholder="Enter barcode (optional)"
          />

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={isLoading}
              disabled={!formData.name || formData.price <= 0}
            >
              {product ? 'Update Product' : 'Add Product'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
