import { supabase } from '@/lib/supabase'
import { productService } from './productService'
import { customerService } from './customerService'
import { debtService } from './debtService'
import { paymentService } from './paymentService'

export interface DashboardStats {
  // Overview Stats
  total_customers: number
  active_customers: number
  total_products: number
  active_products: number
  low_stock_products: number
  
  // Financial Stats
  total_debt_amount: number
  total_payments_amount: number
  outstanding_debt: number
  today_payments: number
  this_month_revenue: number
  
  // Transaction Stats
  total_debt_transactions: number
  unpaid_debt_transactions: number
  total_payments: number
  payment_rate: number
}

export interface SalesAnalytics {
  daily_sales: Array<{
    date: string
    total_amount: number
    transaction_count: number
  }>
  monthly_sales: Array<{
    month: string
    total_amount: number
    transaction_count: number
  }>
  top_products: Array<{
    product_name: string
    total_quantity: number
    total_amount: number
    transaction_count: number
  }>
  payment_methods: Array<{
    method: string
    total_amount: number
    transaction_count: number
    percentage: number
  }>
}

export interface CustomerAnalytics {
  top_debtors: Array<{
    customer_name: string
    total_debt: number
    debt_count: number
    last_payment_date?: string
  }>
  customer_segments: {
    no_debt: number
    low_debt: number
    medium_debt: number
    high_debt: number
  }
  payment_behavior: Array<{
    customer_name: string
    total_borrowed: number
    total_paid: number
    payment_rate: number
    avg_payment_time: number
  }>
}

export interface InventoryAnalytics {
  stock_levels: Array<{
    product_name: string
    current_stock: number
    category: string
    status: 'critical' | 'low' | 'normal' | 'high'
  }>
  inventory_movements: Array<{
    date: string
    product_name: string
    movement_type: string
    quantity: number
    reason: string
  }>
  category_performance: Array<{
    category_name: string
    total_products: number
    total_stock_value: number
    avg_stock_level: number
  }>
}

export const analyticsService = {
  // Get comprehensive dashboard statistics
  getDashboardStats: async (): Promise<DashboardStats> => {
    try {
      const [
        customerStats,
        productStats,
        debtStats,
        paymentStats
      ] = await Promise.all([
        customerService.getCustomerStats(),
        productService.getLowStockProducts(10),
        debtService.getDebtStats(),
        paymentService.getPaymentStats()
      ])

      // Get product counts
      const { count: totalProducts } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })

      const { count: activeProducts } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      return {
        // Customer Stats
        total_customers: customerStats.total_customers,
        active_customers: customerStats.active_customers,
        
        // Product Stats
        total_products: totalProducts || 0,
        active_products: activeProducts || 0,
        low_stock_products: productStats.length,
        
        // Financial Stats
        total_debt_amount: debtStats.total_debt_amount,
        total_payments_amount: paymentStats.total_payment_amount,
        outstanding_debt: debtStats.unpaid_debt_amount,
        today_payments: paymentStats.today_payment_amount,
        this_month_revenue: paymentStats.this_month_payment_amount,
        
        // Transaction Stats
        total_debt_transactions: debtStats.total_debts,
        unpaid_debt_transactions: debtStats.unpaid_debts,
        total_payments: paymentStats.total_payments,
        payment_rate: debtStats.payment_rate
      }
    } catch (error) {
      console.error('Failed to get dashboard stats:', error)
      throw new Error('Failed to load dashboard statistics')
    }
  },

  // Get sales analytics
  getSalesAnalytics: async (days: number = 30): Promise<SalesAnalytics> => {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      // Daily sales from debt transactions
      const { data: dailySales } = await supabase
        .from('debt_transactions')
        .select('debt_date, total_amount')
        .gte('debt_date', startDate.toISOString())
        .order('debt_date')

      // Group by date
      const dailySalesMap = new Map()
      dailySales?.forEach(transaction => {
        const date = transaction.debt_date.split('T')[0]
        if (!dailySalesMap.has(date)) {
          dailySalesMap.set(date, { total_amount: 0, transaction_count: 0 })
        }
        const current = dailySalesMap.get(date)
        current.total_amount += transaction.total_amount
        current.transaction_count += 1
      })

      const daily_sales = Array.from(dailySalesMap.entries()).map(([date, data]) => ({
        date,
        total_amount: data.total_amount,
        transaction_count: data.transaction_count
      }))

      // Monthly sales
      const { data: monthlySales } = await supabase
        .from('debt_transactions')
        .select('debt_date, total_amount')
        .gte('debt_date', new Date(new Date().getFullYear(), 0, 1).toISOString())
        .order('debt_date')

      const monthlySalesMap = new Map()
      monthlySales?.forEach(transaction => {
        const month = transaction.debt_date.substring(0, 7) // YYYY-MM
        if (!monthlySalesMap.has(month)) {
          monthlySalesMap.set(month, { total_amount: 0, transaction_count: 0 })
        }
        const current = monthlySalesMap.get(month)
        current.total_amount += transaction.total_amount
        current.transaction_count += 1
      })

      const monthly_sales = Array.from(monthlySalesMap.entries()).map(([month, data]) => ({
        month,
        total_amount: data.total_amount,
        transaction_count: data.transaction_count
      }))

      // Top products
      const { data: topProducts } = await supabase
        .from('debt_transactions')
        .select('product_name, quantity, total_amount')
        .gte('debt_date', startDate.toISOString())

      const productMap = new Map()
      topProducts?.forEach(transaction => {
        if (!productMap.has(transaction.product_name)) {
          productMap.set(transaction.product_name, {
            total_quantity: 0,
            total_amount: 0,
            transaction_count: 0
          })
        }
        const current = productMap.get(transaction.product_name)
        current.total_quantity += transaction.quantity
        current.total_amount += transaction.total_amount
        current.transaction_count += 1
      })

      const top_products = Array.from(productMap.entries())
        .map(([product_name, data]) => ({
          product_name,
          total_quantity: data.total_quantity,
          total_amount: data.total_amount,
          transaction_count: data.transaction_count
        }))
        .sort((a, b) => b.total_amount - a.total_amount)
        .slice(0, 10)

      // Payment methods
      const { data: paymentMethods } = await supabase
        .from('payments')
        .select('payment_method, amount_paid')
        .gte('payment_date', startDate.toISOString())

      const methodMap = new Map()
      let totalPayments = 0
      paymentMethods?.forEach(payment => {
        if (!methodMap.has(payment.payment_method)) {
          methodMap.set(payment.payment_method, {
            total_amount: 0,
            transaction_count: 0
          })
        }
        const current = methodMap.get(payment.payment_method)
        current.total_amount += payment.amount_paid
        current.transaction_count += 1
        totalPayments += payment.amount_paid
      })

      const payment_methods = Array.from(methodMap.entries()).map(([method, data]) => ({
        method,
        total_amount: data.total_amount,
        transaction_count: data.transaction_count,
        percentage: totalPayments > 0 ? (data.total_amount / totalPayments) * 100 : 0
      }))

      return {
        daily_sales,
        monthly_sales,
        top_products,
        payment_methods
      }
    } catch (error) {
      console.error('Failed to get sales analytics:', error)
      throw new Error('Failed to load sales analytics')
    }
  },

  // Get customer analytics
  getCustomerAnalytics: async (): Promise<CustomerAnalytics> => {
    try {
      // Top debtors
      const { data: topDebtors } = await supabase
        .from('customers')
        .select(`
          full_name,
          total_debt,
          payments(payment_date)
        `)
        .gt('total_debt', 0)
        .eq('is_active', true)
        .order('total_debt', { ascending: false })
        .limit(10)

      const top_debtors = topDebtors?.map(customer => ({
        customer_name: customer.full_name,
        total_debt: customer.total_debt,
        debt_count: 1, // This would need a more complex query
        last_payment_date: customer.payments?.[0]?.payment_date
      })) || []

      // Customer segments
      const { data: allCustomers } = await supabase
        .from('customers')
        .select('total_debt')
        .eq('is_active', true)

      const customer_segments = {
        no_debt: 0,
        low_debt: 0,
        medium_debt: 0,
        high_debt: 0
      }

      allCustomers?.forEach(customer => {
        if (customer.total_debt === 0) {
          customer_segments.no_debt++
        } else if (customer.total_debt <= 500) {
          customer_segments.low_debt++
        } else if (customer.total_debt <= 2000) {
          customer_segments.medium_debt++
        } else {
          customer_segments.high_debt++
        }
      })

      // Payment behavior (simplified)
      const payment_behavior = top_debtors.slice(0, 5).map(debtor => ({
        customer_name: debtor.customer_name,
        total_borrowed: debtor.total_debt * 1.5, // Estimated
        total_paid: debtor.total_debt * 0.5, // Estimated
        payment_rate: 33.3, // Estimated
        avg_payment_time: 15 // Estimated days
      }))

      return {
        top_debtors,
        customer_segments,
        payment_behavior
      }
    } catch (error) {
      console.error('Failed to get customer analytics:', error)
      throw new Error('Failed to load customer analytics')
    }
  },

  // Get inventory analytics
  getInventoryAnalytics: async (): Promise<InventoryAnalytics> => {
    try {
      // Stock levels
      const { data: products } = await supabase
        .from('products')
        .select(`
          name,
          stock_quantity,
          category:categories(name)
        `)
        .eq('is_active', true)

      const stock_levels = products?.map(product => ({
        product_name: product.name,
        current_stock: product.stock_quantity,
        category: (product.category as any)?.name || 'Uncategorized',
        status: product.stock_quantity <= 5 ? 'critical' as const :
                product.stock_quantity <= 10 ? 'low' as const :
                product.stock_quantity <= 50 ? 'normal' as const : 'high' as const
      })) || []

      // Recent inventory movements
      const { data: movements } = await supabase
        .from('inventory_movements')
        .select(`
          created_at,
          movement_type,
          quantity,
          reason,
          product:products(name)
        `)
        .order('created_at', { ascending: false })
        .limit(20)

      const inventory_movements = movements?.map(movement => ({
        date: movement.created_at,
        product_name: (movement.product as any)?.name || 'Unknown',
        movement_type: movement.movement_type,
        quantity: movement.quantity,
        reason: movement.reason || 'No reason specified'
      })) || []

      // Category performance
      const { data: categories } = await supabase
        .from('categories')
        .select(`
          name,
          products(stock_quantity, price)
        `)

      const category_performance = categories?.map(category => {
        const products = category.products || []
        const total_products = products.length
        const total_stock_value = products.reduce((sum, product) => 
          sum + (product.stock_quantity * product.price), 0)
        const avg_stock_level = products.length > 0 
          ? products.reduce((sum, product) => sum + product.stock_quantity, 0) / products.length
          : 0

        return {
          category_name: category.name,
          total_products,
          total_stock_value,
          avg_stock_level
        }
      }) || []

      return {
        stock_levels,
        inventory_movements,
        category_performance
      }
    } catch (error) {
      console.error('Failed to get inventory analytics:', error)
      throw new Error('Failed to load inventory analytics')
    }
  },

  // Generate business report
  generateBusinessReport: async (startDate: string, endDate: string) => {
    try {
      const [
        dashboardStats,
        salesAnalytics,
        customerAnalytics,
        inventoryAnalytics
      ] = await Promise.all([
        analyticsService.getDashboardStats(),
        analyticsService.getSalesAnalytics(30),
        analyticsService.getCustomerAnalytics(),
        analyticsService.getInventoryAnalytics()
      ])

      return {
        report_period: { startDate, endDate },
        generated_at: new Date().toISOString(),
        dashboard_stats: dashboardStats,
        sales_analytics: salesAnalytics,
        customer_analytics: customerAnalytics,
        inventory_analytics: inventoryAnalytics,
        summary: {
          total_revenue: salesAnalytics.daily_sales.reduce((sum, day) => sum + day.total_amount, 0),
          total_transactions: salesAnalytics.daily_sales.reduce((sum, day) => sum + day.transaction_count, 0),
          avg_transaction_value: salesAnalytics.daily_sales.length > 0 
            ? salesAnalytics.daily_sales.reduce((sum, day) => sum + day.total_amount, 0) / 
              salesAnalytics.daily_sales.reduce((sum, day) => sum + day.transaction_count, 0)
            : 0,
          top_performing_product: salesAnalytics.top_products[0]?.product_name || 'N/A',
          most_used_payment_method: salesAnalytics.payment_methods[0]?.method || 'N/A'
        }
      }
    } catch (error) {
      console.error('Failed to generate business report:', error)
      throw new Error('Failed to generate business report')
    }
  }
}
