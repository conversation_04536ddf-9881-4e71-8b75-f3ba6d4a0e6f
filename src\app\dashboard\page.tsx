'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth, usePermissions } from '@/components/providers/AuthProvider'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { MetricCard, SimpleBarChart, SimplePieChart, SimpleLineChart } from '@/components/charts/SimpleChart'
import { analyticsService } from '@/lib/services/analyticsService'
import { formatCurrency, formatDate } from '@/utils'
import type { DashboardStats, SalesAnalytics, CustomerAnalytics } from '@/lib/services/analyticsService'

function DashboardContent() {
  const { user } = useAuth()
  const { canPerformAction } = usePermissions()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [salesAnalytics, setSalesAnalytics] = useState<SalesAnalytics | null>(null)
  const [customerAnalytics, setCustomerAnalytics] = useState<CustomerAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      const [dashboardStats, salesData, customerData] = await Promise.all([
        analyticsService.getDashboardStats(),
        analyticsService.getSalesAnalytics(30),
        analyticsService.getCustomerAnalytics()
      ])

      setStats(dashboardStats)
      setSalesAnalytics(salesData)
      setCustomerAnalytics(customerData)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data')
      console.error('Dashboard data loading error:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-500">Loading dashboard...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.profile?.full_name || user?.email}!
        </h1>
        <p className="text-gray-600 mt-2">
          Role: <span className="font-medium capitalize">{user?.profile?.role}</span>
        </p>
        <p className="text-sm text-gray-500 mt-1">
          Last updated: {formatDate(new Date().toISOString())}
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Key Metrics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <MetricCard
            title="Total Customers"
            value={stats.active_customers}
            icon={
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            }
            color="blue"
          />

          <MetricCard
            title="Active Products"
            value={stats.active_products}
            icon={
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            }
            color="green"
          />

          <MetricCard
            title="Outstanding Debt"
            value={formatCurrency(stats.outstanding_debt, 'PHP')}
            icon={
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            }
            color="red"
          />

          <MetricCard
            title="Today's Payments"
            value={formatCurrency(stats.today_payments, 'PHP')}
            icon={
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            }
            color="green"
          />
        </div>
      )}

      {/* Additional Metrics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <MetricCard
            title="Payment Rate"
            value={`${stats.payment_rate.toFixed(1)}%`}
            icon={
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }
            color="purple"
          />

          <MetricCard
            title="Low Stock Items"
            value={stats.low_stock_products}
            icon={
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            }
            color="yellow"
          />

          <MetricCard
            title="This Month Revenue"
            value={formatCurrency(stats.this_month_revenue, 'PHP')}
            icon={
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            }
            color="green"
          />
        </div>
      )}

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Sales Trend */}
        {salesAnalytics && (
          <SimpleLineChart
            data={salesAnalytics.daily_sales.slice(-7).map(day => ({
              label: new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
              value: day.total_amount
            }))}
            title="Sales Trend (Last 7 Days)"
            formatValue={(value) => formatCurrency(value, 'PHP')}
            color="#10B981"
          />
        )}

        {/* Top Products */}
        {salesAnalytics && (
          <SimpleBarChart
            data={salesAnalytics.top_products.slice(0, 5).map(product => ({
              label: product.product_name.length > 15
                ? `${product.product_name.substring(0, 15)}...`
                : product.product_name,
              value: product.total_amount
            }))}
            title="Top Products (Last 30 Days)"
            formatValue={(value) => formatCurrency(value, 'PHP')}
          />
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Payment Methods */}
        {salesAnalytics && (
          <SimplePieChart
            data={salesAnalytics.payment_methods.map(method => ({
              label: method.method.replace('_', ' ').toUpperCase(),
              value: method.total_amount
            }))}
            title="Payment Methods Distribution"
            formatValue={(value) => formatCurrency(value, 'PHP')}
          />
        )}

        {/* Customer Segments */}
        {customerAnalytics && (
          <SimplePieChart
            data={[
              { label: 'No Debt', value: customerAnalytics.customer_segments.no_debt },
              { label: 'Low Debt', value: customerAnalytics.customer_segments.low_debt },
              { label: 'Medium Debt', value: customerAnalytics.customer_segments.medium_debt },
              { label: 'High Debt', value: customerAnalytics.customer_segments.high_debt }
            ]}
            title="Customer Debt Segments"
            formatValue={(value) => `${value} customers`}
          />
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {canPerformAction('create_product') && (
          <Card>
            <CardHeader>
              <CardTitle>Product Management</CardTitle>
              <CardDescription>
                Add, edit, and manage your store inventory
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/products">
                  <Button className="w-full" variant="outline">
                    View Products
                  </Button>
                </Link>
                <Link href="/products/new">
                  <Button className="w-full">
                    Add New Product
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}

        {canPerformAction('create_customer') && (
          <Card>
            <CardHeader>
              <CardTitle>Customer Management</CardTitle>
              <CardDescription>
                Manage customer information and profiles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/customers">
                  <Button className="w-full" variant="outline">
                    View Customers
                  </Button>
                </Link>
                <Link href="/customers/new">
                  <Button className="w-full">
                    Add New Customer
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}

        {canPerformAction('create_debt') && (
          <Card>
            <CardHeader>
              <CardTitle>Debt Management</CardTitle>
              <CardDescription>
                Track customer debts and payment history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/debts">
                  <Button className="w-full" variant="outline">
                    View Debts
                  </Button>
                </Link>
                <Link href="/debts/new">
                  <Button className="w-full">
                    Record New Debt
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}

        {canPerformAction('process_payment') && (
          <Card>
            <CardHeader>
              <CardTitle>Payment Processing</CardTitle>
              <CardDescription>
                Process customer payments and update balances
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/payments">
                  <Button className="w-full" variant="outline">
                    View Payments
                  </Button>
                </Link>
                <Link href="/payments/new">
                  <Button className="w-full">
                    Process Payment
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}

        {canPerformAction('view_analytics') && (
          <Card>
            <CardHeader>
              <CardTitle>Reports & Analytics</CardTitle>
              <CardDescription>
                View sales reports and business analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/reports">
                  <Button className="w-full" variant="outline">
                    View Reports
                  </Button>
                </Link>
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={loadDashboardData}
                >
                  Refresh Data
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {canPerformAction('manage_users') && (
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                Manage admin users and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button className="w-full" variant="outline">
                  View Users
                </Button>
                <Button className="w-full">
                  Add New User
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <ProtectedRoute requiredRole={['super_admin', 'admin', 'cashier']}>
      <DashboardContent />
    </ProtectedRoute>
  )
}
