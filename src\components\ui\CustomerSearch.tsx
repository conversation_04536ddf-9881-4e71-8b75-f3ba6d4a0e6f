'use client'

import { useState, useEffect, useRef } from 'react'
import { customerService } from '@/lib/services/customerService'
import { formatCurrency } from '@/utils'
import type { Customer } from '@/types'

interface CustomerSearchProps {
  onSelect: (customer: Customer) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  showDebtInfo?: boolean
}

export function CustomerSearch({
  onSelect,
  placeholder = 'Search customers...',
  disabled = false,
  className = '',
  showDebtInfo = true
}: CustomerSearchProps) {
  const [query, setQuery] = useState('')
  const [customers, setCustomers] = useState<Customer[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  
  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  useEffect(() => {
    if (query.length >= 2) {
      searchCustomers()
    } else {
      setCustomers([])
      setIsOpen(false)
    }
  }, [query])

  const searchCustomers = async () => {
    try {
      setLoading(true)
      const results = await customerService.searchCustomers(query, 10)
      setCustomers(results)
      setIsOpen(true)
      setSelectedIndex(-1)
    } catch (error) {
      console.error('Failed to search customers:', error)
      setCustomers([])
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || customers.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < customers.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : prev)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && selectedIndex < customers.length) {
          handleSelectCustomer(customers[selectedIndex])
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        break
    }
  }

  const handleSelectCustomer = (customer: Customer) => {
    setQuery(customer.full_name)
    setIsOpen(false)
    setSelectedIndex(-1)
    onSelect(customer)
  }

  const clearSearch = () => {
    setQuery('')
    setCustomers([])
    setIsOpen(false)
    setSelectedIndex(-1)
    inputRef.current?.focus()
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
        />
        
        {/* Search/Loading Icon */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          ) : query ? (
            <button
              onClick={clearSearch}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          ) : (
            <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          )}
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {customers.length === 0 ? (
            <div className="px-4 py-3 text-sm text-gray-500">
              {loading ? 'Searching...' : 'No customers found'}
            </div>
          ) : (
            customers.map((customer, index) => (
              <div
                key={customer.id}
                onClick={() => handleSelectCustomer(customer)}
                className={`px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 ${
                  index === selectedIndex 
                    ? 'bg-blue-50 text-blue-900' 
                    : 'hover:bg-gray-50'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      {customer.full_name}
                    </div>
                    {customer.phone && (
                      <div className="text-sm text-gray-500">
                        {customer.phone}
                      </div>
                    )}
                    {customer.address && (
                      <div className="text-sm text-gray-500 truncate">
                        {customer.address}
                      </div>
                    )}
                  </div>
                  
                  {showDebtInfo && (
                    <div className="ml-4 text-right">
                      <div className={`text-sm font-medium ${
                        customer.total_debt > 0 ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {formatCurrency(customer.total_debt, 'PHP')}
                      </div>
                      <div className="text-xs text-gray-500">
                        {customer.total_debt > 0 ? 'Outstanding' : 'No debt'}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  )
}
