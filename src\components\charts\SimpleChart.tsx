'use client'

import { useMemo } from 'react'
import { formatCurrency } from '@/utils'

interface ChartData {
  label: string
  value: number
  color?: string
}

interface SimpleBarChartProps {
  data: ChartData[]
  title?: string
  height?: number
  showValues?: boolean
  formatValue?: (value: number) => string
}

export function SimpleBarChart({ 
  data, 
  title, 
  height = 200, 
  showValues = true,
  formatValue = (value) => value.toString()
}: SimpleBarChartProps) {
  const maxValue = useMemo(() => Math.max(...data.map(d => d.value)), [data])
  
  const colors = [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
  ]

  return (
    <div className="bg-white p-4 rounded-lg border">
      {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
      <div className="space-y-3" style={{ height }}>
        {data.map((item, index) => (
          <div key={item.label} className="flex items-center space-x-3">
            <div className="w-20 text-sm text-gray-600 truncate" title={item.label}>
              {item.label}
            </div>
            <div className="flex-1 flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                <div
                  className="h-6 rounded-full transition-all duration-300"
                  style={{
                    width: `${(item.value / maxValue) * 100}%`,
                    backgroundColor: item.color || colors[index % colors.length]
                  }}
                />
                {showValues && (
                  <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                    {formatValue(item.value)}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

interface SimplePieChartProps {
  data: ChartData[]
  title?: string
  size?: number
  showLegend?: boolean
  formatValue?: (value: number) => string
}

export function SimplePieChart({ 
  data, 
  title, 
  size = 200, 
  showLegend = true,
  formatValue = (value) => value.toString()
}: SimplePieChartProps) {
  const total = useMemo(() => data.reduce((sum, d) => sum + d.value, 0), [data])
  
  const colors = [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
  ]

  let cumulativePercentage = 0

  return (
    <div className="bg-white p-4 rounded-lg border">
      {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
      <div className="flex items-center space-x-6">
        <div className="relative" style={{ width: size, height: size }}>
          <svg width={size} height={size} className="transform -rotate-90">
            <circle
              cx={size / 2}
              cy={size / 2}
              r={size / 2 - 10}
              fill="none"
              stroke="#E5E7EB"
              strokeWidth="20"
            />
            {data.map((item, index) => {
              const percentage = (item.value / total) * 100
              const strokeDasharray = `${percentage * 2.51} 251.2` // 2π * r ≈ 251.2 for r = 40
              const strokeDashoffset = -cumulativePercentage * 2.51
              cumulativePercentage += percentage

              return (
                <circle
                  key={item.label}
                  cx={size / 2}
                  cy={size / 2}
                  r={size / 2 - 10}
                  fill="none"
                  stroke={item.color || colors[index % colors.length]}
                  strokeWidth="20"
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset={strokeDashoffset}
                  className="transition-all duration-300"
                />
              )
            })}
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold">{data.length}</div>
              <div className="text-sm text-gray-500">Items</div>
            </div>
          </div>
        </div>

        {showLegend && (
          <div className="space-y-2">
            {data.map((item, index) => (
              <div key={item.label} className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color || colors[index % colors.length] }}
                />
                <span className="text-sm text-gray-700">{item.label}</span>
                <span className="text-sm font-medium">
                  {formatValue(item.value)} ({((item.value / total) * 100).toFixed(1)}%)
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

interface SimpleLineChartProps {
  data: Array<{ label: string; value: number }>
  title?: string
  height?: number
  color?: string
  formatValue?: (value: number) => string
}

export function SimpleLineChart({ 
  data, 
  title, 
  height = 200, 
  color = '#3B82F6',
  formatValue = (value) => value.toString()
}: SimpleLineChartProps) {
  const maxValue = useMemo(() => Math.max(...data.map(d => d.value)), [data])
  const minValue = useMemo(() => Math.min(...data.map(d => d.value)), [data])
  const range = maxValue - minValue

  const points = data.map((item, index) => {
    const x = (index / (data.length - 1)) * 100
    const y = range > 0 ? ((maxValue - item.value) / range) * 80 + 10 : 50
    return `${x},${y}`
  }).join(' ')

  return (
    <div className="bg-white p-4 rounded-lg border">
      {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
      <div className="relative" style={{ height }}>
        <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
          <polyline
            fill="none"
            stroke={color}
            strokeWidth="2"
            points={points}
            className="transition-all duration-300"
          />
          {data.map((item, index) => {
            const x = (index / (data.length - 1)) * 100
            const y = range > 0 ? ((maxValue - item.value) / range) * 80 + 10 : 50
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="1"
                fill={color}
                className="transition-all duration-300"
              />
            )
          })}
        </svg>
        
        {/* X-axis labels */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500">
          {data.map((item, index) => (
            <span key={index} className="transform -translate-x-1/2">
              {item.label}
            </span>
          ))}
        </div>
      </div>
      
      {/* Y-axis info */}
      <div className="mt-2 flex justify-between text-xs text-gray-500">
        <span>Min: {formatValue(minValue)}</span>
        <span>Max: {formatValue(maxValue)}</span>
      </div>
    </div>
  )
}

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  changeLabel?: string
  icon?: React.ReactNode
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple'
}

export function MetricCard({ 
  title, 
  value, 
  change, 
  changeLabel, 
  icon, 
  color = 'blue' 
}: MetricCardProps) {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    red: 'bg-red-50 text-red-600',
    yellow: 'bg-yellow-50 text-yellow-600',
    purple: 'bg-purple-50 text-purple-600'
  }

  return (
    <div className="bg-white p-6 rounded-lg border">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900">{value}</p>
          {change !== undefined && (
            <div className="flex items-center mt-2">
              <span className={`text-sm font-medium ${
                change >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {change >= 0 ? '+' : ''}{change.toFixed(1)}%
              </span>
              {changeLabel && (
                <span className="text-sm text-gray-500 ml-2">{changeLabel}</span>
              )}
            </div>
          )}
        </div>
        {icon && (
          <div className={`p-3 rounded-full ${colorClasses[color]}`}>
            {icon}
          </div>
        )}
      </div>
    </div>
  )
}
