'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { usePermissions } from '@/components/providers/AuthProvider'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { DataTable } from '@/components/ui/DataTable'
import { customerService } from '@/lib/services/customerService'
import { formatCurrency, formatDate } from '@/utils'
import type { CustomerDebtSummary, DebtTransaction, Payment } from '@/types'

function CustomerDetailContent() {
  const params = useParams()
  const router = useRouter()
  const { canPerformAction } = usePermissions()
  const [customerData, setCustomerData] = useState<CustomerDebtSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const customerId = params.id as string

  useEffect(() => {
    if (customerId) {
      loadCustomerData()
    }
  }, [customerId])

  const loadCustomerData = async () => {
    try {
      setLoading(true)
      const data = await customerService.getCustomerWithDebts(customerId)
      setCustomerData(data)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load customer data')
      console.error('Failed to load customer data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-500">Loading customer data...</span>
        </div>
      </div>
    )
  }

  if (error || !customerData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Customer Not Found</h1>
          <p className="text-gray-600 mb-6">{error || 'The requested customer could not be found.'}</p>
          <Link href="/customers">
            <Button>Back to Customers</Button>
          </Link>
        </div>
      </div>
    )
  }

  const { customer, active_debts, recent_payments, debt_statistics } = customerData

  const debtColumns = [
    {
      key: 'product_name',
      header: 'Product',
      render: (debt: DebtTransaction) => (
        <div>
          <div className="font-medium text-gray-900">{debt.product_name}</div>
          <div className="text-sm text-gray-500">Qty: {debt.quantity}</div>
        </div>
      )
    },
    {
      key: 'product_price',
      header: 'Unit Price',
      render: (debt: DebtTransaction) => (
        <span className="font-medium">{formatCurrency(debt.product_price, 'PHP')}</span>
      )
    },
    {
      key: 'total_amount',
      header: 'Total Amount',
      render: (debt: DebtTransaction) => (
        <span className="font-medium">{formatCurrency(debt.total_amount, 'PHP')}</span>
      )
    },
    {
      key: 'remaining_balance',
      header: 'Remaining Balance',
      render: (debt: DebtTransaction) => (
        <span className="font-medium text-red-600">
          {formatCurrency(debt.remaining_balance, 'PHP')}
        </span>
      )
    },
    {
      key: 'debt_date',
      header: 'Date',
      render: (debt: DebtTransaction) => (
        <span className="text-sm text-gray-900">{formatDate(debt.debt_date)}</span>
      )
    }
  ]

  const paymentColumns = [
    {
      key: 'amount_paid',
      header: 'Amount Paid',
      render: (payment: Payment) => (
        <span className="font-medium text-green-600">
          {formatCurrency(payment.amount_paid, 'PHP')}
        </span>
      )
    },
    {
      key: 'payment_method',
      header: 'Method',
      render: (payment: Payment) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
          {payment.payment_method}
        </span>
      )
    },
    {
      key: 'payment_date',
      header: 'Date',
      render: (payment: Payment) => (
        <span className="text-sm text-gray-900">{formatDate(payment.payment_date)}</span>
      )
    },
    {
      key: 'notes',
      header: 'Notes',
      render: (payment: Payment) => (
        <span className="text-sm text-gray-600">{payment.notes || '-'}</span>
      )
    }
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{customer.full_name}</h1>
          <p className="text-gray-600 mt-2">Customer Details & Debt Information</p>
        </div>
        <div className="flex space-x-4">
          {canPerformAction('edit_customer') && (
            <Link href={`/customers/${customer.id}/edit`}>
              <Button variant="outline">Edit Customer</Button>
            </Link>
          )}
          {canPerformAction('process_payment') && customer.total_debt > 0 && (
            <Link href={`/payments/new?customer_id=${customer.id}`}>
              <Button>Process Payment</Button>
            </Link>
          )}
          <Link href="/customers">
            <Button variant="outline">Back to Customers</Button>
          </Link>
        </div>
      </div>

      {/* Customer Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Full Name</label>
              <p className="text-gray-900">{customer.full_name}</p>
            </div>
            {customer.phone && (
              <div>
                <label className="text-sm font-medium text-gray-500">Phone</label>
                <p className="text-gray-900">{customer.phone}</p>
              </div>
            )}
            {customer.email && (
              <div>
                <label className="text-sm font-medium text-gray-500">Email</label>
                <p className="text-gray-900">{customer.email}</p>
              </div>
            )}
            {customer.address && (
              <div>
                <label className="text-sm font-medium text-gray-500">Address</label>
                <p className="text-gray-900">{customer.address}</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Account Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <p className={`font-medium ${customer.is_active ? 'text-green-600' : 'text-red-600'}`}>
                {customer.is_active ? 'Active' : 'Inactive'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Member Since</label>
              <p className="text-gray-900">{formatDate(customer.created_at)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Current Debt</label>
              <p className={`text-2xl font-bold ${customer.total_debt > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {formatCurrency(customer.total_debt, 'PHP')}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Debt Statistics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Total Transactions</label>
              <p className="text-gray-900 font-medium">{debt_statistics.total_transactions}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Total Borrowed</label>
              <p className="text-gray-900 font-medium">
                {formatCurrency(debt_statistics.total_amount_borrowed, 'PHP')}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Total Paid</label>
              <p className="text-green-600 font-medium">
                {formatCurrency(debt_statistics.total_amount_paid, 'PHP')}
              </p>
            </div>
            {debt_statistics.oldest_debt_date && (
              <div>
                <label className="text-sm font-medium text-gray-500">Oldest Debt</label>
                <p className="text-gray-900">{formatDate(debt_statistics.oldest_debt_date)}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Active Debts */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Outstanding Debts</CardTitle>
          <CardDescription>
            Current unpaid debts for this customer
          </CardDescription>
        </CardHeader>
        <CardContent>
          {active_debts.length > 0 ? (
            <DataTable
              data={active_debts}
              columns={debtColumns}
              emptyMessage="No outstanding debts"
            />
          ) : (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No Outstanding Debts</h3>
              <p className="mt-1 text-sm text-gray-500">This customer has no unpaid debts.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Payments */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Payments</CardTitle>
          <CardDescription>
            Last 10 payments made by this customer
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recent_payments.length > 0 ? (
            <DataTable
              data={recent_payments}
              columns={paymentColumns}
              emptyMessage="No payments found"
            />
          ) : (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No Payments Found</h3>
              <p className="mt-1 text-sm text-gray-500">This customer has not made any payments yet.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default function CustomerDetailPage() {
  return (
    <ProtectedRoute requiredPermission="view_analytics">
      <CustomerDetailContent />
    </ProtectedRoute>
  )
}
