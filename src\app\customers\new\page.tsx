'use client'

import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { CustomerForm } from '@/components/forms/CustomerForm'

function AddCustomerContent() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Add New Customer</h1>
        <p className="text-gray-600 mt-2">Add a new customer to the system</p>
      </div>

      <CustomerForm />
    </div>
  )
}

export default function AddCustomerPage() {
  return (
    <ProtectedRoute requiredPermission="create_customer">
      <AddCustomerContent />
    </ProtectedRoute>
  )
}
