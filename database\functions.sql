-- Business Logic Functions for Sari-Sari Store Admin Dashboard

-- Function to update customer total debt
CREATE OR REPLACE FUNCTION update_customer_total_debt()
RETURNS TRIGGER AS $$
BEGIN
    -- Update customer's total debt based on remaining balances
    UPDATE customers 
    SET total_debt = (
        SELECT COALESCE(SUM(remaining_balance), 0)
        FROM debt_transactions 
        WHERE customer_id = COALESCE(NEW.customer_id, OLD.customer_id)
        AND is_fully_paid = false
    )
    WHERE id = COALESCE(NEW.customer_id, OLD.customer_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to update customer total debt when debt transactions change
CREATE TRIGGER trigger_update_customer_debt_on_debt_change
    AFTER INSERT OR UPDATE OR DELETE ON debt_transactions
    FOR EACH ROW EXECUTE FUNCTION update_customer_total_debt();

-- Function to process payment and update debt balances
CREATE OR REPLACE FUNCTION process_payment(
    p_customer_id UUID,
    p_amount_paid DECIMAL(10,2),
    p_payment_method VARCHAR(50) DEFAULT 'cash',
    p_notes TEXT DEFAULT NULL,
    p_recorded_by VARCHAR(255) DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    remaining_amount DECIMAL(10,2) := p_amount_paid;
    debt_record RECORD;
    payment_id UUID;
    total_applied DECIMAL(10,2) := 0;
    debts_updated INTEGER := 0;
BEGIN
    -- Validate customer exists
    IF NOT EXISTS (SELECT 1 FROM customers WHERE id = p_customer_id) THEN
        RETURN json_build_object('success', false, 'error', 'Customer not found');
    END IF;
    
    -- Validate payment amount
    IF p_amount_paid <= 0 THEN
        RETURN json_build_object('success', false, 'error', 'Payment amount must be greater than 0');
    END IF;
    
    -- Insert payment record
    INSERT INTO payments (customer_id, amount_paid, payment_method, notes, recorded_by)
    VALUES (p_customer_id, p_amount_paid, p_payment_method, p_notes, p_recorded_by)
    RETURNING id INTO payment_id;
    
    -- Apply payment to oldest debts first (FIFO)
    FOR debt_record IN 
        SELECT id, remaining_balance 
        FROM debt_transactions 
        WHERE customer_id = p_customer_id 
        AND is_fully_paid = false 
        AND remaining_balance > 0
        ORDER BY debt_date ASC
    LOOP
        IF remaining_amount <= 0 THEN
            EXIT;
        END IF;
        
        IF remaining_amount >= debt_record.remaining_balance THEN
            -- Fully pay this debt
            remaining_amount := remaining_amount - debt_record.remaining_balance;
            total_applied := total_applied + debt_record.remaining_balance;
            
            UPDATE debt_transactions 
            SET remaining_balance = 0, 
                is_fully_paid = true,
                updated_at = NOW()
            WHERE id = debt_record.id;
            
            -- Link payment to this specific debt
            UPDATE payments 
            SET debt_transaction_id = debt_record.id 
            WHERE id = payment_id AND debt_transaction_id IS NULL;
            
        ELSE
            -- Partially pay this debt
            total_applied := total_applied + remaining_amount;
            
            UPDATE debt_transactions 
            SET remaining_balance = remaining_balance - remaining_amount,
                updated_at = NOW()
            WHERE id = debt_record.id;
            
            remaining_amount := 0;
            
            -- Link payment to this specific debt
            UPDATE payments 
            SET debt_transaction_id = debt_record.id 
            WHERE id = payment_id AND debt_transaction_id IS NULL;
        END IF;
        
        debts_updated := debts_updated + 1;
    END LOOP;
    
    RETURN json_build_object(
        'success', true,
        'payment_id', payment_id,
        'amount_applied', total_applied,
        'remaining_amount', remaining_amount,
        'debts_updated', debts_updated
    );
END;
$$ LANGUAGE plpgsql;

-- Function to create debt transaction and update inventory
CREATE OR REPLACE FUNCTION create_debt_transaction(
    p_customer_id UUID,
    p_product_id UUID,
    p_quantity INTEGER,
    p_notes TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    product_record RECORD;
    debt_id UUID;
    total_amount DECIMAL(10,2);
BEGIN
    -- Get product details and check stock
    SELECT id, name, price, stock_quantity 
    INTO product_record
    FROM products 
    WHERE id = p_product_id AND is_active = true;
    
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Product not found or inactive');
    END IF;
    
    IF product_record.stock_quantity < p_quantity THEN
        RETURN json_build_object('success', false, 'error', 'Insufficient stock');
    END IF;
    
    -- Calculate total amount
    total_amount := product_record.price * p_quantity;
    
    -- Create debt transaction
    INSERT INTO debt_transactions (
        customer_id, 
        product_id, 
        product_name, 
        product_price, 
        quantity, 
        remaining_balance,
        notes
    )
    VALUES (
        p_customer_id,
        p_product_id,
        product_record.name,
        product_record.price,
        p_quantity,
        total_amount,
        p_notes
    )
    RETURNING id INTO debt_id;
    
    -- Update product stock
    UPDATE products 
    SET stock_quantity = stock_quantity - p_quantity,
        updated_at = NOW()
    WHERE id = p_product_id;
    
    -- Record inventory movement
    INSERT INTO inventory_movements (
        product_id,
        movement_type,
        quantity,
        previous_stock,
        new_stock,
        reason,
        reference_id
    )
    VALUES (
        p_product_id,
        'out',
        p_quantity,
        product_record.stock_quantity,
        product_record.stock_quantity - p_quantity,
        'debt_sale',
        debt_id
    );
    
    RETURN json_build_object(
        'success', true,
        'debt_id', debt_id,
        'total_amount', total_amount
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get customer debt summary
CREATE OR REPLACE FUNCTION get_customer_debt_summary(p_customer_id UUID)
RETURNS JSON AS $$
DECLARE
    customer_record RECORD;
    debt_summary JSON;
BEGIN
    -- Get customer details
    SELECT * INTO customer_record FROM customers WHERE id = p_customer_id;
    
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Customer not found');
    END IF;
    
    -- Build comprehensive debt summary
    SELECT json_build_object(
        'customer', json_build_object(
            'id', customer_record.id,
            'full_name', customer_record.full_name,
            'total_debt', customer_record.total_debt
        ),
        'active_debts', (
            SELECT COALESCE(json_agg(
                json_build_object(
                    'id', dt.id,
                    'product_name', dt.product_name,
                    'quantity', dt.quantity,
                    'product_price', dt.product_price,
                    'total_amount', dt.total_amount,
                    'remaining_balance', dt.remaining_balance,
                    'debt_date', dt.debt_date
                )
            ), '[]'::json)
            FROM debt_transactions dt
            WHERE dt.customer_id = p_customer_id 
            AND dt.is_fully_paid = false
            ORDER BY dt.debt_date DESC
        ),
        'recent_payments', (
            SELECT COALESCE(json_agg(
                json_build_object(
                    'id', p.id,
                    'amount_paid', p.amount_paid,
                    'payment_date', p.payment_date,
                    'payment_method', p.payment_method
                )
            ), '[]'::json)
            FROM payments p
            WHERE p.customer_id = p_customer_id
            ORDER BY p.payment_date DESC
            LIMIT 10
        ),
        'debt_statistics', (
            SELECT json_build_object(
                'total_transactions', COUNT(*),
                'total_amount_borrowed', COALESCE(SUM(total_amount), 0),
                'total_amount_paid', COALESCE(SUM(total_amount - remaining_balance), 0),
                'oldest_debt_date', MIN(debt_date)
            )
            FROM debt_transactions
            WHERE customer_id = p_customer_id
        )
    ) INTO debt_summary;
    
    RETURN json_build_object('success', true, 'data', debt_summary);
END;
$$ LANGUAGE plpgsql;
