'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { usePermissions } from '@/components/providers/AuthProvider'
import { Button } from '@/components/ui/Button'
import { DataTable } from '@/components/ui/DataTable'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { productService, categoryService } from '@/lib/services/productService'
import { formatCurrency } from '@/utils'
import type { Product, Category, ProductFilters, PaginationParams } from '@/types'

function ProductsContent() {
  const { canPerformAction } = usePermissions()
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })
  const [filters, setFilters] = useState<ProductFilters>({})
  const [error, setError] = useState('')

  useEffect(() => {
    loadProducts()
    loadCategories()
  }, [pagination.page, pagination.limit, filters])

  const loadProducts = async () => {
    try {
      setLoading(true)
      const params: PaginationParams & ProductFilters = {
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      }
      
      const response = await productService.getProducts(params)
      setProducts(response.data)
      setPagination(response.pagination)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load products')
      console.error('Failed to load products:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      const data = await categoryService.getCategories()
      setCategories(data)
    } catch (error) {
      console.error('Failed to load categories:', error)
    }
  }

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setFilters(prev => ({ ...prev, sort_by: column, sort_order: direction }))
  }

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }))
  }

  const handleDeleteProduct = async (product: Product) => {
    if (!canPerformAction('delete_product')) {
      alert('You do not have permission to delete products')
      return
    }

    if (!confirm(`Are you sure you want to delete "${product.name}"?`)) {
      return
    }

    try {
      await productService.deleteProduct(product.id)
      loadProducts() // Reload the list
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to delete product')
    }
  }

  const columns = [
    {
      key: 'image_url',
      header: 'Image',
      render: (product: Product) => (
        <div className="w-12 h-12 relative">
          {product.image_url ? (
            <Image
              src={product.image_url}
              alt={product.name}
              fill
              className="object-cover rounded-md"
            />
          ) : (
            <div className="w-full h-full bg-gray-200 rounded-md flex items-center justify-center">
              <svg className="w-6 h-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'name',
      header: 'Product Name',
      sortable: true,
      render: (product: Product) => (
        <div>
          <div className="font-medium text-gray-900">{product.name}</div>
          {product.net_weight && (
            <div className="text-sm text-gray-500">{product.net_weight}</div>
          )}
        </div>
      )
    },
    {
      key: 'category',
      header: 'Category',
      render: (product: Product) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {product.category?.name || 'Uncategorized'}
        </span>
      )
    },
    {
      key: 'price',
      header: 'Price',
      sortable: true,
      render: (product: Product) => (
        <span className="font-medium">{formatCurrency(product.price, 'PHP')}</span>
      )
    },
    {
      key: 'stock_quantity',
      header: 'Stock',
      sortable: true,
      render: (product: Product) => (
        <span className={`font-medium ${
          product.stock_quantity <= 10 
            ? 'text-red-600' 
            : product.stock_quantity <= 20 
            ? 'text-yellow-600' 
            : 'text-green-600'
        }`}>
          {product.stock_quantity}
        </span>
      )
    },
    {
      key: 'is_active',
      header: 'Status',
      render: (product: Product) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          product.is_active 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {product.is_active ? 'Active' : 'Inactive'}
        </span>
      )
    }
  ]

  const renderActions = (product: Product) => (
    <div className="flex items-center space-x-2">
      <Link href={`/products/${product.id}`}>
        <Button variant="outline" size="sm">
          View
        </Button>
      </Link>
      {canPerformAction('edit_product') && (
        <Link href={`/products/${product.id}/edit`}>
          <Button variant="outline" size="sm">
            Edit
          </Button>
        </Link>
      )}
      {canPerformAction('delete_product') && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleDeleteProduct(product)}
          className="text-red-600 hover:text-red-700"
        >
          Delete
        </Button>
      )}
    </div>
  )

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Products</h1>
          <p className="text-gray-600 mt-2">Manage your store inventory</p>
        </div>
        {canPerformAction('create_product') && (
          <Link href="/products/new">
            <Button>Add New Product</Button>
          </Link>
        )}
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter products by category and status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                value={filters.category_id || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, category_id: e.target.value || undefined }))}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={filters.is_active?.toString() || ''}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  is_active: e.target.value ? e.target.value === 'true' : undefined 
                }))}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Status</option>
                <option value="true">Active</option>
                <option value="false">Inactive</option>
              </select>
            </div>

            <div className="flex items-end">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.low_stock || false}
                  onChange={(e) => setFilters(prev => ({ ...prev, low_stock: e.target.checked || undefined }))}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">Low Stock Only</span>
              </label>
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setFilters({})
                  setPagination(prev => ({ ...prev, page: 1 }))
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <DataTable
        data={products}
        columns={columns}
        loading={loading}
        pagination={pagination}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        onSort={handleSort}
        searchPlaceholder="Search products..."
        emptyMessage="No products found"
        actions={renderActions}
      />
    </div>
  )
}

export default function ProductsPage() {
  return (
    <ProtectedRoute requiredPermission="view_analytics">
      <ProductsContent />
    </ProtectedRoute>
  )
}
