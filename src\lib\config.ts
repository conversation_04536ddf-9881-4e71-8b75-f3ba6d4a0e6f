/**
 * Application Configuration and Environment Variable Validation
 * 
 * This module centralizes all environment variable handling and validation
 * to ensure consistent configuration across the application.
 */

export interface AppConfig {
  supabase: {
    url: string
    anonKey: string
    serviceRoleKey?: string
  }
  cloudinary: {
    cloudName?: string
    apiKey?: string
    apiSecret?: string
    uploadPreset?: string
  }
  app: {
    url: string
    environment: 'development' | 'production' | 'test'
  }
}

/**
 * Validates and returns the application configuration
 * Throws detailed errors if required environment variables are missing
 */
export function getAppConfig(): AppConfig {
  // Supabase Configuration (Required)
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  // Cloudinary Configuration (Optional)
  const cloudinaryCloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME
  const cloudinaryApiKey = process.env.CLOUDINARY_API_KEY
  const cloudinaryApiSecret = process.env.CLOUDINARY_API_SECRET
  const cloudinaryUploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET

  // App Configuration
  const appUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
  const nodeEnv = process.env.NODE_ENV || 'development'

  // Validate required Supabase configuration
  if (!supabaseUrl) {
    throw new Error(
      '🔧 Missing NEXT_PUBLIC_SUPABASE_URL environment variable.\n\n' +
      'Please add the following to your .env.local file:\n' +
      'NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co\n\n' +
      'You can find this URL in your Supabase dashboard under Settings → API'
    )
  }

  if (!supabaseAnonKey) {
    throw new Error(
      '🔧 Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable.\n\n' +
      'Please add the following to your .env.local file:\n' +
      'NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here\n\n' +
      'You can find this key in your Supabase dashboard under Settings → API'
    )
  }

  // Validate URL format
  try {
    new URL(supabaseUrl)
  } catch {
    throw new Error(
      `🔧 Invalid NEXT_PUBLIC_SUPABASE_URL format: "${supabaseUrl}"\n\n` +
      'Expected format: https://your-project-ref.supabase.co\n' +
      'Please check your .env.local file and ensure the URL is correct.'
    )
  }

  // Validate JWT format (basic check)
  if (!supabaseAnonKey.startsWith('eyJ')) {
    throw new Error(
      '🔧 Invalid NEXT_PUBLIC_SUPABASE_ANON_KEY format.\n\n' +
      'The anon key should be a JWT token starting with "eyJ".\n' +
      'Please check your .env.local file and ensure you copied the correct key from Supabase.'
    )
  }

  // Warn about missing optional configuration
  if (!cloudinaryCloudName && typeof window !== 'undefined') {
    console.warn(
      '⚠️  Cloudinary not configured. Image upload features will not work.\n' +
      'To enable image uploads, add Cloudinary configuration to your .env.local file.'
    )
  }

  return {
    supabase: {
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      serviceRoleKey: supabaseServiceRoleKey,
    },
    cloudinary: {
      cloudName: cloudinaryCloudName,
      apiKey: cloudinaryApiKey,
      apiSecret: cloudinaryApiSecret,
      uploadPreset: cloudinaryUploadPreset,
    },
    app: {
      url: appUrl,
      environment: nodeEnv as 'development' | 'production' | 'test',
    },
  }
}

/**
 * Validates configuration without throwing errors
 * Returns validation result with detailed messages
 */
export function validateConfig(): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  try {
    getAppConfig()
  } catch (error) {
    errors.push(error instanceof Error ? error.message : 'Unknown configuration error')
  }

  // Check for optional but recommended configuration
  if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME) {
    warnings.push('Cloudinary configuration missing - image uploads will not work')
  }

  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    warnings.push('Service role key missing - some admin operations may not work')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  }
}

/**
 * Development helper to check configuration status
 */
export function checkConfigurationStatus(): void {
  if (process.env.NODE_ENV === 'development') {
    const { isValid, errors, warnings } = validateConfig()

    if (!isValid) {
      console.error('❌ Configuration Errors:')
      errors.forEach(error => console.error(`  ${error}`))
    } else {
      console.log('✅ Configuration is valid')
    }

    if (warnings.length > 0) {
      console.warn('⚠️  Configuration Warnings:')
      warnings.forEach(warning => console.warn(`  ${warning}`))
    }
  }
}

// Export the validated configuration
export const config = getAppConfig()
