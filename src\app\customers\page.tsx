'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { usePermissions } from '@/components/providers/AuthProvider'
import { Button } from '@/components/ui/Button'
import { DataTable } from '@/components/ui/DataTable'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { customerService } from '@/lib/services/customerService'
import { formatCurrency, formatDate } from '@/utils'
import type { Customer, CustomerFilters, PaginationParams } from '@/types'

function CustomersContent() {
  const { canPerformAction } = usePermissions()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })
  const [filters, setFilters] = useState<CustomerFilters>({})
  const [error, setError] = useState('')
  const [stats, setStats] = useState({
    total_customers: 0,
    active_customers: 0,
    customers_with_debt: 0,
    total_debt_amount: 0,
    average_debt: 0
  })

  useEffect(() => {
    loadCustomers()
    loadStats()
  }, [pagination.page, pagination.limit, filters])

  const loadCustomers = async () => {
    try {
      setLoading(true)
      const params: PaginationParams & CustomerFilters = {
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      }
      
      const response = await customerService.getCustomers(params)
      setCustomers(response.data)
      setPagination(response.pagination)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load customers')
      console.error('Failed to load customers:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const statsData = await customerService.getCustomerStats()
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load customer stats:', error)
    }
  }

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setFilters(prev => ({ ...prev, sort_by: column, sort_order: direction }))
  }

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }))
  }

  const handleDeleteCustomer = async (customer: Customer) => {
    if (!canPerformAction('delete_customer')) {
      alert('You do not have permission to delete customers')
      return
    }

    if (customer.total_debt > 0) {
      alert(`Cannot delete customer "${customer.full_name}" with outstanding debt of ${formatCurrency(customer.total_debt, 'PHP')}`)
      return
    }

    if (!confirm(`Are you sure you want to deactivate "${customer.full_name}"?`)) {
      return
    }

    try {
      await customerService.deleteCustomer(customer.id)
      loadCustomers() // Reload the list
      loadStats() // Reload stats
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to delete customer')
    }
  }

  const handleReactivateCustomer = async (customer: Customer) => {
    if (!canPerformAction('edit_customer')) {
      alert('You do not have permission to reactivate customers')
      return
    }

    try {
      await customerService.reactivateCustomer(customer.id)
      loadCustomers() // Reload the list
      loadStats() // Reload stats
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to reactivate customer')
    }
  }

  const columns = [
    {
      key: 'full_name',
      header: 'Customer Name',
      sortable: true,
      render: (customer: Customer) => (
        <div>
          <div className="font-medium text-gray-900">{customer.full_name}</div>
          {customer.phone && (
            <div className="text-sm text-gray-500">{customer.phone}</div>
          )}
        </div>
      )
    },
    {
      key: 'email',
      header: 'Email',
      render: (customer: Customer) => (
        <span className="text-gray-900">{customer.email || '-'}</span>
      )
    },
    {
      key: 'address',
      header: 'Address',
      render: (customer: Customer) => (
        <span className="text-gray-900 text-sm">
          {customer.address ? (
            customer.address.length > 50 
              ? `${customer.address.substring(0, 50)}...`
              : customer.address
          ) : '-'}
        </span>
      )
    },
    {
      key: 'total_debt',
      header: 'Outstanding Debt',
      sortable: true,
      render: (customer: Customer) => (
        <span className={`font-medium ${
          customer.total_debt > 0 
            ? 'text-red-600' 
            : 'text-green-600'
        }`}>
          {formatCurrency(customer.total_debt, 'PHP')}
        </span>
      )
    },
    {
      key: 'created_at',
      header: 'Date Added',
      sortable: true,
      render: (customer: Customer) => (
        <span className="text-gray-900 text-sm">
          {formatDate(customer.created_at)}
        </span>
      )
    },
    {
      key: 'is_active',
      header: 'Status',
      render: (customer: Customer) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          customer.is_active 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {customer.is_active ? 'Active' : 'Inactive'}
        </span>
      )
    }
  ]

  const renderActions = (customer: Customer) => (
    <div className="flex items-center space-x-2">
      <Link href={`/customers/${customer.id}`}>
        <Button variant="outline" size="sm">
          View
        </Button>
      </Link>
      {canPerformAction('edit_customer') && (
        <Link href={`/customers/${customer.id}/edit`}>
          <Button variant="outline" size="sm">
            Edit
          </Button>
        </Link>
      )}
      {customer.is_active ? (
        canPerformAction('delete_customer') && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteCustomer(customer)}
            className="text-red-600 hover:text-red-700"
          >
            Deactivate
          </Button>
        )
      ) : (
        canPerformAction('edit_customer') && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleReactivateCustomer(customer)}
            className="text-green-600 hover:text-green-700"
          >
            Reactivate
          </Button>
        )
      )}
    </div>
  )

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Customers</h1>
          <p className="text-gray-600 mt-2">Manage customer information and debt tracking</p>
        </div>
        {canPerformAction('create_customer') && (
          <Link href="/customers/new">
            <Button>Add New Customer</Button>
          </Link>
        )}
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <svg className="h-4 w-4 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total_customers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
            <svg className="h-4 w-4 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.active_customers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">With Debt</CardTitle>
            <svg className="h-4 w-4 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.customers_with_debt}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Debt</CardTitle>
            <svg className="h-4 w-4 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.total_debt_amount, 'PHP')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Debt</CardTitle>
            <svg className="h-4 w-4 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.average_debt, 'PHP')}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter customers by debt status and account status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Debt Status
              </label>
              <select
                value={filters.has_debt?.toString() || ''}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  has_debt: e.target.value ? e.target.value === 'true' : undefined 
                }))}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Customers</option>
                <option value="true">With Debt</option>
                <option value="false">No Debt</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Account Status
              </label>
              <select
                value={filters.is_active?.toString() || ''}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  is_active: e.target.value ? e.target.value === 'true' : undefined 
                }))}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Status</option>
                <option value="true">Active</option>
                <option value="false">Inactive</option>
              </select>
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setFilters({})
                  setPagination(prev => ({ ...prev, page: 1 }))
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <DataTable
        data={customers}
        columns={columns}
        loading={loading}
        pagination={pagination}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        onSort={handleSort}
        searchPlaceholder="Search customers..."
        emptyMessage="No customers found"
        actions={renderActions}
      />
    </div>
  )
}

export default function CustomersPage() {
  return (
    <ProtectedRoute requiredPermission="view_analytics">
      <CustomersContent />
    </ProtectedRoute>
  )
}
