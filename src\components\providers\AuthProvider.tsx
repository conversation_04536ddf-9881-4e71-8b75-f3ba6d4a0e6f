'use client'

import { createContext, useContext } from 'react'
import { useAuth as useAuthHook, authService, hasPermission, type AuthUser } from '@/lib/auth'
import { handleError, ErrorType } from '@/lib/error-handler'

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<any>
  signUp: (email: string, password: string, fullName?: string) => Promise<any>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  updatePassword: (password: string) => Promise<void>
  updateProfile: (updates: any) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuthHook()

  const signIn = async (email: string, password: string) => {
    try {
      return await authService.signIn(email, password)
    } catch (error) {
      const appError = handleError(error as Error, { action: 'signIn', email })
      throw appError
    }
  }

  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      return await authService.signUp(email, password, fullName)
    } catch (error) {
      const appError = handleError(error as Error, { action: 'signUp', email })
      throw appError
    }
  }

  const signOut = async () => {
    try {
      return await authService.signOut()
    } catch (error) {
      const appError = handleError(error as Error, { action: 'signOut' })
      throw appError
    }
  }

  const resetPassword = async (email: string) => {
    try {
      return await authService.resetPassword(email)
    } catch (error) {
      const appError = handleError(error as Error, { action: 'resetPassword', email })
      throw appError
    }
  }

  const updatePassword = async (password: string) => {
    try {
      return await authService.updatePassword(password)
    } catch (error) {
      const appError = handleError(error as Error, { action: 'updatePassword' })
      throw appError
    }
  }

  const updateProfile = async (updates: any) => {
    try {
      return await authService.updateProfile(updates)
    } catch (error) {
      const appError = handleError(error as Error, { action: 'updateProfile', updates })
      throw appError
    }
  }

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook for checking permissions
export function usePermissions() {
  const { user } = useAuth()

  const hasRole = (requiredRole: string | string[]): boolean => {
    if (!user?.profile) return false

    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
    return roles.includes(user.profile.role)
  }

  const canPerformAction = (action: string): boolean => {
    if (!user) return false
    return hasPermission(user, action)
  }

  const isSuperAdmin = (): boolean => hasRole('super_admin')
  const isAdmin = (): boolean => hasRole(['super_admin', 'admin'])
  const isCashier = (): boolean => hasRole('cashier')

  return {
    user,
    hasRole,
    canPerformAction,
    isSuperAdmin,
    isAdmin,
    isCashier,
  }
}
