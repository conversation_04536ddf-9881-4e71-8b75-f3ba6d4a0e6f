'use client'

import { createContext, useContext } from 'react'
import { useAuth as useAuthHook, authService, hasPermission, type AuthUser } from '@/lib/auth'

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<any>
  signUp: (email: string, password: string, fullName?: string) => Promise<any>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  updatePassword: (password: string) => Promise<void>
  updateProfile: (updates: any) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuthHook()

  const signIn = async (email: string, password: string) => {
    return await authService.signIn(email, password)
  }

  const signUp = async (email: string, password: string, fullName?: string) => {
    return await authService.signUp(email, password, fullName)
  }

  const signOut = async () => {
    return await authService.signOut()
  }

  const resetPassword = async (email: string) => {
    return await authService.resetPassword(email)
  }

  const updatePassword = async (password: string) => {
    return await authService.updatePassword(password)
  }

  const updateProfile = async (updates: any) => {
    return await authService.updateProfile(updates)
  }

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook for checking permissions
export function usePermissions() {
  const { user } = useAuth()

  const hasRole = (requiredRole: string | string[]): boolean => {
    if (!user?.profile) return false

    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
    return roles.includes(user.profile.role)
  }

  const canPerformAction = (action: string): boolean => {
    if (!user) return false
    return hasPermission(user, action)
  }

  const isSuperAdmin = (): boolean => hasRole('super_admin')
  const isAdmin = (): boolean => hasRole(['super_admin', 'admin'])
  const isCashier = (): boolean => hasRole('cashier')

  return {
    user,
    hasRole,
    canPerformAction,
    isSuperAdmin,
    isAdmin,
    isCashier
  }
}
