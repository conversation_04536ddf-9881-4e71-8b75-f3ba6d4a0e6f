import { supabase, dbFunctions } from '@/lib/supabase'
import type { Customer, CustomerFormData, CustomerFilters, PaginationParams, PaginatedResponse, CustomerDebtSummary } from '@/types'

export const customerService = {
  // Get all customers with pagination and filters
  getCustomers: async (
    params: PaginationParams & CustomerFilters = { page: 1, limit: 10 }
  ): Promise<PaginatedResponse<Customer>> => {
    const { page, limit, search, has_debt, is_active, sort_by = 'family_name', sort_order = 'asc' } = params
    
    let query = supabase
      .from('customers')
      .select('*', { count: 'exact' })

    // Apply filters
    if (search) {
      query = query.or(`first_name.ilike.%${search}%,family_name.ilike.%${search}%,full_name.ilike.%${search}%,phone.ilike.%${search}%`)
    }
    
    if (typeof has_debt === 'boolean') {
      if (has_debt) {
        query = query.gt('total_debt', 0)
      } else {
        query = query.eq('total_debt', 0)
      }
    }
    
    if (typeof is_active === 'boolean') {
      query = query.eq('is_active', is_active)
    }

    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      throw new Error(error.message)
    }

    return {
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    }
  },

  // Get single customer by ID
  getCustomer: async (id: string): Promise<Customer | null> => {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw new Error(error.message)
    }

    return data
  },

  // Get customer with debt details
  getCustomerWithDebts: async (id: string): Promise<CustomerDebtSummary | null> => {
    const { data, error } = await dbFunctions.getCustomerDebtSummary(id)

    if (error) {
      throw new Error(error.message)
    }

    if (!data?.success) {
      throw new Error(data?.error || 'Failed to get customer debt summary')
    }

    return data.data
  },

  // Create new customer
  createCustomer: async (customerData: CustomerFormData): Promise<Customer> => {
    const { data, error } = await supabase
      .from('customers')
      .insert([customerData])
      .select()
      .single()

    if (error) {
      if (error.code === '23505') {
        throw new Error('A customer with this name already exists')
      }
      throw new Error(error.message)
    }

    return data
  },

  // Update customer
  updateCustomer: async (id: string, customerData: Partial<CustomerFormData>): Promise<Customer> => {
    const { data, error } = await supabase
      .from('customers')
      .update(customerData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      if (error.code === '23505') {
        throw new Error('A customer with this name already exists')
      }
      throw new Error(error.message)
    }

    return data
  },

  // Delete customer (soft delete by setting is_active to false)
  deleteCustomer: async (id: string): Promise<void> => {
    // Check if customer has outstanding debts
    const customer = await customerService.getCustomer(id)
    if (customer && customer.total_debt > 0) {
      throw new Error('Cannot delete customer with outstanding debts')
    }

    const { error } = await supabase
      .from('customers')
      .update({ is_active: false })
      .eq('id', id)

    if (error) {
      throw new Error(error.message)
    }
  },

  // Permanently delete customer (only for super admin)
  permanentlyDeleteCustomer: async (id: string): Promise<void> => {
    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(error.message)
    }
  },

  // Reactivate customer
  reactivateCustomer: async (id: string): Promise<Customer> => {
    const { data, error } = await supabase
      .from('customers')
      .update({ is_active: true })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return data
  },

  // Get customers with outstanding debts
  getCustomersWithDebts: async (): Promise<Customer[]> => {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .gt('total_debt', 0)
      .eq('is_active', true)
      .order('total_debt', { ascending: false })

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Search customers by name
  searchCustomers: async (query: string, limit: number = 10): Promise<Customer[]> => {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .or(`first_name.ilike.%${query}%,family_name.ilike.%${query}%,full_name.ilike.%${query}%`)
      .eq('is_active', true)
      .order('family_name')
      .limit(limit)

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Get customer statistics
  getCustomerStats: async () => {
    const [
      { count: totalCustomers },
      { count: activeCustomers },
      { count: customersWithDebt },
      { data: totalDebtData }
    ] = await Promise.all([
      supabase.from('customers').select('*', { count: 'exact', head: true }),
      supabase.from('customers').select('*', { count: 'exact', head: true }).eq('is_active', true),
      supabase.from('customers').select('*', { count: 'exact', head: true }).gt('total_debt', 0).eq('is_active', true),
      supabase.from('customers').select('total_debt').eq('is_active', true)
    ])

    const totalDebtAmount = totalDebtData?.reduce((sum, customer) => sum + (customer.total_debt || 0), 0) || 0
    const averageDebt = customersWithDebt ? totalDebtAmount / customersWithDebt : 0

    return {
      total_customers: totalCustomers || 0,
      active_customers: activeCustomers || 0,
      customers_with_debt: customersWithDebt || 0,
      total_debt_amount: totalDebtAmount,
      average_debt: averageDebt
    }
  },

  // Get recent customers
  getRecentCustomers: async (limit: number = 5): Promise<Customer[]> => {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Bulk update customers
  bulkUpdateCustomers: async (updates: Array<{ id: string; data: Partial<CustomerFormData> }>): Promise<Customer[]> => {
    const results = await Promise.all(
      updates.map(({ id, data }) => customerService.updateCustomer(id, data))
    )
    return results
  },

  // Export customers data
  exportCustomers: async (filters: CustomerFilters = {}): Promise<Customer[]> => {
    let query = supabase
      .from('customers')
      .select('*')

    // Apply filters
    if (filters.search) {
      query = query.or(`first_name.ilike.%${filters.search}%,family_name.ilike.%${filters.search}%,full_name.ilike.%${filters.search}%`)
    }
    
    if (typeof filters.has_debt === 'boolean') {
      if (filters.has_debt) {
        query = query.gt('total_debt', 0)
      } else {
        query = query.eq('total_debt', 0)
      }
    }
    
    if (typeof filters.is_active === 'boolean') {
      query = query.eq('is_active', filters.is_active)
    }

    query = query.order('family_name')

    const { data, error } = await query

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  }
}
