'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { usePermissions } from '@/components/providers/AuthProvider'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { paymentService } from '@/lib/services/paymentService'
import { formatCurrency, formatDate } from '@/utils'
import type { Payment } from '@/types'

function PaymentDetailContent() {
  const params = useParams()
  const router = useRouter()
  const { canPerformAction } = usePermissions()
  const [payment, setPayment] = useState<Payment | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const paymentId = params.id as string

  useEffect(() => {
    if (paymentId) {
      loadPaymentData()
    }
  }, [paymentId])

  const loadPaymentData = async () => {
    try {
      setLoading(true)
      const data = await paymentService.getPayment(paymentId)
      setPayment(data)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load payment data')
      console.error('Failed to load payment data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-500">Loading payment data...</span>
        </div>
      </div>
    )
  }

  if (error || !payment) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Payment Not Found</h1>
          <p className="text-gray-600 mb-6">{error || 'The requested payment could not be found.'}</p>
          <Link href="/payments">
            <Button>Back to Payments</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Payment Details</h1>
          <p className="text-gray-600 mt-2">Payment ID: {payment.id}</p>
        </div>
        <div className="flex space-x-4">
          {canPerformAction('edit_payment') && (
            <Link href={`/payments/${payment.id}/edit`}>
              <Button variant="outline">Edit Payment</Button>
            </Link>
          )}
          <Link href="/payments">
            <Button variant="outline">Back to Payments</Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Payment Information */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Amount Paid</label>
              <p className="text-3xl font-bold text-green-600">
                {formatCurrency(payment.amount_paid, 'PHP')}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Payment Method</label>
              <p className="text-gray-900 capitalize">
                {payment.payment_method.replace('_', ' ')}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Payment Date</label>
              <p className="text-gray-900">{formatDate(payment.payment_date)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Recorded By</label>
              <p className="text-gray-900">{payment.recorded_by || 'System'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Record Created</label>
              <p className="text-gray-900">{formatDate(payment.created_at)}</p>
            </div>
          </CardContent>
        </Card>

        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Customer Name</label>
              <p className="text-gray-900 font-medium">{payment.customer?.full_name}</p>
            </div>
            {payment.customer?.phone && (
              <div>
                <label className="text-sm font-medium text-gray-500">Phone</label>
                <p className="text-gray-900">{payment.customer.phone}</p>
              </div>
            )}
            {payment.customer?.email && (
              <div>
                <label className="text-sm font-medium text-gray-500">Email</label>
                <p className="text-gray-900">{payment.customer.email}</p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-gray-500">Current Total Debt</label>
              <p className={`font-bold text-lg ${
                payment.customer?.total_debt && payment.customer.total_debt > 0 
                  ? 'text-red-600' 
                  : 'text-green-600'
              }`}>
                {formatCurrency(payment.customer?.total_debt || 0, 'PHP')}
              </p>
            </div>
            <div className="pt-2">
              <Link href={`/customers/${payment.customer?.id}`}>
                <Button variant="outline" size="sm">
                  View Customer Profile
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Related Debt Transaction */}
      {payment.debt_transaction && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Related Debt Transaction</CardTitle>
            <CardDescription>
              The debt transaction this payment was applied to
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <label className="text-sm font-medium text-gray-500">Product</label>
                <p className="text-gray-900 font-medium">{payment.debt_transaction.product_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Total Amount</label>
                <p className="text-gray-900">{formatCurrency(payment.debt_transaction.total_amount, 'PHP')}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Remaining Balance</label>
                <p className={`font-medium ${
                  payment.debt_transaction.remaining_balance > 0 ? 'text-red-600' : 'text-green-600'
                }`}>
                  {formatCurrency(payment.debt_transaction.remaining_balance, 'PHP')}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  payment.debt_transaction.is_fully_paid 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {payment.debt_transaction.is_fully_paid ? 'Fully Paid' : 'Outstanding'}
                </span>
              </div>
            </div>
            <div className="mt-4">
              <Link href={`/debts/${payment.debt_transaction.id}`}>
                <Button variant="outline" size="sm">
                  View Debt Transaction
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payment Notes */}
      {payment.notes && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Payment Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-900 bg-gray-50 p-4 rounded-md">{payment.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Payment Receipt */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Receipt</CardTitle>
          <CardDescription>
            Official record of this payment transaction
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-white border-2 border-gray-200 rounded-lg p-6 space-y-4">
            <div className="text-center border-b pb-4">
              <h3 className="text-xl font-bold text-gray-900">Payment Receipt</h3>
              <p className="text-gray-600">Sari-Sari Store Admin System</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Receipt #:</span>
                <span className="ml-2 text-gray-900">{payment.id.slice(-8).toUpperCase()}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Date:</span>
                <span className="ml-2 text-gray-900">{formatDate(payment.payment_date)}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Customer:</span>
                <span className="ml-2 text-gray-900">{payment.customer?.full_name}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Method:</span>
                <span className="ml-2 text-gray-900 capitalize">{payment.payment_method.replace('_', ' ')}</span>
              </div>
            </div>

            <div className="border-t pt-4">
              <div className="flex justify-between items-center">
                <span className="text-lg font-medium text-gray-700">Amount Paid:</span>
                <span className="text-2xl font-bold text-green-600">
                  {formatCurrency(payment.amount_paid, 'PHP')}
                </span>
              </div>
            </div>

            <div className="border-t pt-4 text-center text-xs text-gray-500">
              <p>Thank you for your payment!</p>
              <p>Recorded by: {payment.recorded_by || 'System'}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function PaymentDetailPage() {
  return (
    <ProtectedRoute requiredPermission="view_analytics">
      <PaymentDetailContent />
    </ProtectedRoute>
  )
}
