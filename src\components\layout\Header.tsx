'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { useAuth } from '@/components/providers/AuthProvider'
import { APP_NAME } from '@/constants'

export function Header() {
  const { user, signOut } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSignOut = async () => {
    setIsLoading(true)
    try {
      await signOut()
      router.push('/auth/login')
    } catch (error) {
      console.error('Sign out error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <header className="border-b border-gray-200 bg-white shadow-sm">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link href={user ? "/dashboard" : "/"} className="text-xl font-bold text-gray-900">
              {APP_NAME}
            </Link>
            {user && (
              <span className="ml-2 text-sm text-gray-500">Admin Dashboard</span>
            )}
          </div>

          {user && (
            <nav className="hidden md:flex space-x-8">
              <Link href="/dashboard" className="text-gray-700 hover:text-gray-900">
                Dashboard
              </Link>
              <Link href="/products" className="text-gray-700 hover:text-gray-900">
                Products
              </Link>
              <Link href="/customers" className="text-gray-700 hover:text-gray-900">
                Customers
              </Link>
              <Link href="/debts" className="text-gray-700 hover:text-gray-900">
                Debts
              </Link>
              <Link href="/payments" className="text-gray-700 hover:text-gray-900">
                Payments
              </Link>
            </nav>
          )}

          <div className="flex items-center space-x-4">
            {user ? (
              <div className="flex items-center space-x-4">
                <div className="text-sm">
                  <p className="font-medium text-gray-900">
                    {user.profile?.full_name || user.email}
                  </p>
                  <p className="text-gray-500 capitalize">
                    {user.profile?.role}
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={handleSignOut}
                  isLoading={isLoading}
                >
                  Sign Out
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link href="/auth/login">
                  <Button variant="ghost">Login</Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
