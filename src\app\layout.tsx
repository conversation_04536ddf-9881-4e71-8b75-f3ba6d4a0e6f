import type { Metadata } from 'next'
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import './globals.css'
import { ReduxProvider } from '@/components/providers/ReduxProvider'
import { AuthProvider } from '@/components/providers/AuthProvider'
import { Layout } from '@/components/layout/Layout'
import { ErrorBoundary } from '@/components/common/ErrorBoundary'
import { checkConfigurationStatus } from '@/lib/config'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Caparan Tindahan - Sari-Sari Store Admin Dashboard',
  description:
    'Professional sari-sari store admin dashboard for product management and customer debt tracking',
}

// Check configuration status in development
if (typeof window === 'undefined') {
  checkConfigurationStatus()
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang='en'>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <ErrorBoundary>
          <AuthProvider>
            <ReduxProvider>
              <Layout>{children}</Layout>
            </ReduxProvider>
          </AuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
