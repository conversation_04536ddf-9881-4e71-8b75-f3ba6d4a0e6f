import { supabase, dbFunctions } from '@/lib/supabase'
import type { Payment, PaymentFormData, PaymentFilters, PaginationParams, PaginatedResponse, Customer, DebtTransaction } from '@/types'

export const paymentService = {
  // Get all payments with pagination and filters
  getPayments: async (
    params: PaginationParams & PaymentFilters = { page: 1, limit: 10 }
  ): Promise<PaginatedResponse<Payment>> => {
    const { page, limit, search, customer_id, payment_method, date_from, date_to, sort_by = 'payment_date', sort_order = 'desc' } = params
    
    let query = supabase
      .from('payments')
      .select(`
        *,
        customer:customers(*),
        debt_transaction:debt_transactions(*)
      `, { count: 'exact' })

    // Apply filters
    if (search) {
      query = query.or(`customers.full_name.ilike.%${search}%,notes.ilike.%${search}%`)
    }
    
    if (customer_id) {
      query = query.eq('customer_id', customer_id)
    }
    
    if (payment_method) {
      query = query.eq('payment_method', payment_method)
    }
    
    if (date_from) {
      query = query.gte('payment_date', date_from)
    }
    
    if (date_to) {
      query = query.lte('payment_date', date_to)
    }

    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      throw new Error(error.message)
    }

    return {
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    }
  },

  // Get single payment by ID
  getPayment: async (id: string): Promise<Payment | null> => {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        customer:customers(*),
        debt_transaction:debt_transactions(*)
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw new Error(error.message)
    }

    return data
  },

  // Process payment using database function
  processPayment: async (paymentData: PaymentFormData): Promise<Payment> => {
    const { data, error } = await dbFunctions.processPayment({
      customer_id: paymentData.customer_id,
      amount_paid: paymentData.amount_paid,
      payment_method: paymentData.payment_method || 'cash',
      notes: paymentData.notes,
      recorded_by: paymentData.recorded_by
    })

    if (error) {
      throw new Error(error.message)
    }

    if (!data?.success) {
      throw new Error(data?.error || 'Failed to process payment')
    }

    // Fetch the created payment with related data
    const createdPayment = await paymentService.getPayment(data.payment_id)
    if (!createdPayment) {
      throw new Error('Failed to retrieve processed payment')
    }

    return createdPayment
  },

  // Update payment (limited fields for safety)
  updatePayment: async (id: string, paymentData: Partial<PaymentFormData>): Promise<Payment> => {
    const { data, error } = await supabase
      .from('payments')
      .update({
        notes: paymentData.notes,
        // Only allow updating notes for safety
      })
      .eq('id', id)
      .select(`
        *,
        customer:customers(*),
        debt_transaction:debt_transactions(*)
      `)
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return data
  },

  // Delete payment (only for super admin and recent payments)
  deletePayment: async (id: string): Promise<void> => {
    const { error } = await supabase
      .from('payments')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(error.message)
    }
  },

  // Get payments for a specific customer
  getCustomerPayments: async (customerId: string): Promise<Payment[]> => {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        customer:customers(*),
        debt_transaction:debt_transactions(*)
      `)
      .eq('customer_id', customerId)
      .order('payment_date', { ascending: false })

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Get payment statistics
  getPaymentStats: async () => {
    const today = new Date().toISOString().split('T')[0]
    const thisMonth = new Date().toISOString().slice(0, 7) + '-01'
    
    const [
      { count: totalPayments },
      { data: totalPaymentAmount },
      { data: todayPayments },
      { data: thisMonthPayments }
    ] = await Promise.all([
      supabase.from('payments').select('*', { count: 'exact', head: true }),
      supabase.from('payments').select('amount_paid'),
      supabase.from('payments').select('amount_paid').gte('payment_date', today),
      supabase.from('payments').select('amount_paid').gte('payment_date', thisMonth)
    ])

    const totalAmount = totalPaymentAmount?.reduce((sum, payment) => sum + (payment.amount_paid || 0), 0) || 0
    const todayAmount = todayPayments?.reduce((sum, payment) => sum + (payment.amount_paid || 0), 0) || 0
    const thisMonthAmount = thisMonthPayments?.reduce((sum, payment) => sum + (payment.amount_paid || 0), 0) || 0

    return {
      total_payments: totalPayments || 0,
      total_payment_amount: totalAmount,
      today_payments: todayPayments?.length || 0,
      today_payment_amount: todayAmount,
      this_month_payments: thisMonthPayments?.length || 0,
      this_month_payment_amount: thisMonthAmount,
      average_payment: totalPayments ? totalAmount / totalPayments : 0
    }
  },

  // Get recent payments
  getRecentPayments: async (limit: number = 10): Promise<Payment[]> => {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        customer:customers(*),
        debt_transaction:debt_transactions(*)
      `)
      .order('payment_date', { ascending: false })
      .limit(limit)

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Get payments by date range
  getPaymentsByDateRange: async (startDate: string, endDate: string): Promise<Payment[]> => {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        customer:customers(*),
        debt_transaction:debt_transactions(*)
      `)
      .gte('payment_date', startDate)
      .lte('payment_date', endDate)
      .order('payment_date', { ascending: false })

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Get payment methods summary
  getPaymentMethodsSummary: async (): Promise<any[]> => {
    const { data, error } = await supabase
      .from('payments')
      .select('payment_method, amount_paid')

    if (error) {
      throw new Error(error.message)
    }

    // Group by payment method manually
    const methodMap = new Map()
    data?.forEach(payment => {
      if (!methodMap.has(payment.payment_method)) {
        methodMap.set(payment.payment_method, { total_amount: 0, count: 0 })
      }
      const current = methodMap.get(payment.payment_method)
      current.total_amount += payment.amount_paid
      current.count += 1
    })

    return Array.from(methodMap.entries()).map(([method, stats]) => ({
      payment_method: method,
      total_amount: stats.total_amount,
      count: stats.count
    })).sort((a, b) => b.total_amount - a.total_amount)
  },

  // Get top paying customers
  getTopPayingCustomers: async (limit: number = 10): Promise<any[]> => {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        customer_id,
        amount_paid,
        customer:customers(full_name, phone)
      `)

    if (error) {
      throw new Error(error.message)
    }

    // Group by customer manually
    const customerMap = new Map()
    data?.forEach(payment => {
      if (!customerMap.has(payment.customer_id)) {
        customerMap.set(payment.customer_id, {
          customer: payment.customer,
          total_amount: 0,
          count: 0
        })
      }
      const current = customerMap.get(payment.customer_id)
      current.total_amount += payment.amount_paid
      current.count += 1
    })

    return Array.from(customerMap.values())
      .sort((a, b) => b.total_amount - a.total_amount)
      .slice(0, limit)
  },

  // Get daily payment summary for charts
  getDailyPaymentSummary: async (days: number = 30): Promise<any[]> => {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const { data, error } = await supabase
      .from('payments')
      .select('payment_date, amount_paid')
      .gte('payment_date', startDate.toISOString().split('T')[0])
      .order('payment_date', { ascending: true })

    if (error) {
      throw new Error(error.message)
    }

    // Group by date manually
    const dateMap = new Map()
    data?.forEach(payment => {
      const date = payment.payment_date.split('T')[0]
      if (!dateMap.has(date)) {
        dateMap.set(date, { total_amount: 0, count: 0 })
      }
      const current = dateMap.get(date)
      current.total_amount += payment.amount_paid
      current.count += 1
    })

    return Array.from(dateMap.entries()).map(([date, stats]) => ({
      payment_date: date,
      total_amount: stats.total_amount,
      count: stats.count
    })).sort((a, b) => a.payment_date.localeCompare(b.payment_date))
  },

  // Export payments data
  exportPayments: async (filters: PaymentFilters = {}): Promise<Payment[]> => {
    let query = supabase
      .from('payments')
      .select(`
        *,
        customer:customers(*),
        debt_transaction:debt_transactions(*)
      `)

    // Apply filters
    if (filters.customer_id) {
      query = query.eq('customer_id', filters.customer_id)
    }
    
    if (filters.payment_method) {
      query = query.eq('payment_method', filters.payment_method)
    }
    
    if (filters.date_from) {
      query = query.gte('payment_date', filters.date_from)
    }
    
    if (filters.date_to) {
      query = query.lte('payment_date', filters.date_to)
    }

    query = query.order('payment_date', { ascending: false })

    const { data, error } = await query

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Get available payment methods
  getPaymentMethods: (): Array<{ value: string; label: string }> => {
    return [
      { value: 'cash', label: 'Cash' },
      { value: 'gcash', label: 'GCash' },
      { value: 'bank_transfer', label: 'Bank Transfer' },
      { value: 'credit_card', label: 'Credit Card' },
      { value: 'debit_card', label: 'Debit Card' },
      { value: 'check', label: 'Check' },
      { value: 'other', label: 'Other' }
    ]
  },

  // Validate payment amount against customer debt
  validatePaymentAmount: async (customerId: string, amount: number): Promise<{ isValid: boolean; error?: string; maxAmount?: number }> => {
    try {
      const { data: customer, error } = await supabase
        .from('customers')
        .select('total_debt')
        .eq('id', customerId)
        .single()

      if (error) {
        return { isValid: false, error: 'Customer not found' }
      }

      if (amount <= 0) {
        return { isValid: false, error: 'Payment amount must be greater than 0' }
      }

      if (amount > customer.total_debt) {
        return { 
          isValid: false, 
          error: `Payment amount cannot exceed customer debt of ₱${customer.total_debt.toFixed(2)}`,
          maxAmount: customer.total_debt
        }
      }

      return { isValid: true }
    } catch (error) {
      return { isValid: false, error: 'Failed to validate payment amount' }
    }
  }
}
