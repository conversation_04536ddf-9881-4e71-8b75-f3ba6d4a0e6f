# Caparan Tindahan 🔥

A professional e-commerce platform built with modern web technologies.

## 🚀 Tech Stack

- **Frontend & Backend**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Database**: PostgreSQL via Supabase
- **State Management**: Redux Toolkit
- **Development**: E<PERSON><PERSON>, Prettier, VS Code configuration

## ✅ Features

- ⚡ **Next.js 15** - Latest version with Turbopack for blazing fast development
- 🔒 **TypeScript** - Full type safety across the entire application
- 🎨 **Tailwind CSS v4** - Modern utility-first CSS framework
- 🔄 **Redux Toolkit** - Predictable state management with modern patterns
- 🗄️ **Supabase** - PostgreSQL database with real-time features
- ⚙️ **Professional Setup** - ESLint, Prettier, and VS Code configuration

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd caparan-tindahan
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env.local
   ```

   Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── layout/           # Layout components
│   ├── providers/        # Context providers
│   └── ui/               # Reusable UI components
├── hooks/                # Custom React hooks
├── lib/                  # Library configurations
│   ├── features/         # Redux slices
│   ├── store.ts          # Redux store
│   └── supabase.ts       # Supabase client
├── types/                # TypeScript type definitions
├── utils/                # Utility functions
└── constants/            # Application constants
```

## 🧰 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting
- `npm run type-check` - Run TypeScript type checking

## 🔧 Development Tools

### ESLint Configuration
- Next.js recommended rules
- TypeScript support
- Prettier integration
- Custom rules for code quality

### Prettier Configuration
- Consistent code formatting
- Single quotes
- No semicolons
- 100 character line width

### VS Code Settings
- Format on save
- ESLint auto-fix
- Tailwind CSS IntelliSense
- TypeScript support

## 🗄️ Database Setup (Supabase)

1. Create a new project at [supabase.com](https://supabase.com)
2. Copy your project URL and API keys
3. Update your `.env.local` file
4. Use the Supabase client in your components:

```typescript
import { useSupabase } from '@/hooks/useSupabase'

function MyComponent() {
  const supabase = useSupabase()
  // Use supabase client here
}
```

## 🔄 State Management (Redux Toolkit)

The application uses Redux Toolkit for state management:

```typescript
import { useAppSelector, useAppDispatch } from '@/lib/store'
import { setUser } from '@/lib/features/auth/authSlice'

function MyComponent() {
  const dispatch = useAppDispatch()
  const { user } = useAppSelector((state) => state.auth)

  // Dispatch actions
  dispatch(setUser({ id: '1', email: '<EMAIL>' }))
}
```

## 🎨 Styling with Tailwind CSS

This project uses Tailwind CSS v4 with CSS-based configuration:

```css
/* In globals.css */
@import "tailwindcss";

@theme inline {
  --color-primary: #3b82f6;
  --color-secondary: #6b7280;
}
```

## 📦 Component Library

The project includes a set of reusable UI components:

- `Button` - Customizable button component
- `Input` - Form input with validation
- `Card` - Content container
- `Layout` - Page layout wrapper

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on every push

### Other Platforms
The application can be deployed to any platform that supports Node.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Open an issue on GitHub
- Contact the development team

---

Built with ❤️ using Next.js 15, TypeScript, and modern web technologies.
