'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from './supabase'
import type { User, Session } from '@supabase/supabase-js'

export interface UserProfile {
  id: string
  email: string
  full_name?: string
  role: 'super_admin' | 'admin' | 'cashier'
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface AuthUser extends User {
  profile?: UserProfile
}

export interface AuthState {
  user: AuthUser | null
  loading: boolean
  session: Session | null
}

// Auth hook
export function useAuth(): AuthState {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [session, setSession] = useState<Session | null>(null)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
          setLoading(false)
          return
        }

        if (session?.user) {
          // Fetch user profile
          const { data: profile, error: profileError } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', session.user.id)
            .single()

          if (profileError) {
            console.error('Error fetching profile:', profileError)
          }

          const authUser: AuthUser = {
            ...session.user,
            profile: profile || undefined
          }

          setUser(authUser)
          setSession(session)
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)
        
        if (session?.user) {
          // Fetch user profile
          const { data: profile, error: profileError } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', session.user.id)
            .single()

          if (profileError) {
            console.error('Error fetching profile:', profileError)
          }

          const authUser: AuthUser = {
            ...session.user,
            profile: profile || undefined
          }

          setUser(authUser)
          setSession(session)
        } else {
          setUser(null)
          setSession(null)
        }
        
        setLoading(false)
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  return { user, loading, session }
}

// Auth functions
export const authService = {
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      throw new Error(error.message)
    }

    return data
  },

  async signUp(email: string, password: string, fullName?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    })

    if (error) {
      throw new Error(error.message)
    }

    return data
  },

  async signOut() {
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      throw new Error(error.message)
    }
  },

  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    })

    if (error) {
      throw new Error(error.message)
    }
  },

  async updatePassword(password: string) {
    const { error } = await supabase.auth.updateUser({
      password,
    })

    if (error) {
      throw new Error(error.message)
    }
  },

  async updateProfile(updates: Partial<UserProfile>) {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('No authenticated user')
    }

    const { error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', user.id)

    if (error) {
      throw new Error(error.message)
    }
  }
}

// Permission utilities
export function hasRole(user: AuthUser, role: UserProfile['role']): boolean {
  if (!user?.profile) return false
  
  const roleHierarchy = {
    super_admin: 3,
    admin: 2,
    cashier: 1
  }
  
  const userRoleLevel = roleHierarchy[user.profile.role]
  const requiredRoleLevel = roleHierarchy[role]
  
  return userRoleLevel >= requiredRoleLevel
}

export function hasPermission(user: AuthUser, permission: string): boolean {
  if (!user?.profile) return false
  
  const permissions = {
    super_admin: [
      'manage_users', 'manage_products', 'manage_categories', 'manage_customers',
      'create_debt', 'edit_debt', 'delete_debt', 'process_payment', 'delete_payment',
      'view_analytics', 'export_data', 'manage_settings'
    ],
    admin: [
      'manage_products', 'manage_categories', 'manage_customers',
      'create_debt', 'edit_debt', 'process_payment',
      'view_analytics', 'export_data'
    ],
    cashier: [
      'create_product', 'edit_product', 'create_customer', 'edit_customer',
      'create_debt', 'process_payment', 'view_analytics'
    ]
  }
  
  return permissions[user.profile.role]?.includes(permission) || false
}

// HOC for protected routes
export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  requiredRole?: UserProfile['role']
) {
  return function AuthenticatedComponent(props: P) {
    const { user, loading } = useAuth()
    const router = useRouter()

    useEffect(() => {
      if (!loading) {
        if (!user) {
          router.push('/login')
          return
        }
        
        if (requiredRole && !hasRole(user, requiredRole)) {
          router.push('/unauthorized')
          return
        }
      }
    }, [user, loading, router])

    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )
    }

    if (!user || (requiredRole && !hasRole(user, requiredRole))) {
      return null
    }

    return <WrappedComponent {...props} />
  }
}
