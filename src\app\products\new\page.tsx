'use client'

import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { ProductForm } from '@/components/forms/ProductForm'

function AddProductContent() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Add New Product</h1>
        <p className="text-gray-600 mt-2">Add a new product to your inventory</p>
      </div>

      <ProductForm />
    </div>
  )
}

export default function AddProductPage() {
  return (
    <ProtectedRoute requiredPermission="create_product">
      <AddProductContent />
    </ProtectedRoute>
  )
}
