-- Sample Data for Sari-Sari Store Admin Dashboard
-- This file contains realistic sample data for testing and development

-- Insert sample categories
INSERT INTO categories (name, description) VALUES
('Snacks', 'Chips, crackers, and other snack foods'),
('Beverages', 'Soft drinks, juices, and water'),
('Canned Goods', 'Canned fish, meat, and vegetables'),
('Instant Foods', 'Instant noodles, coffee, and ready-to-eat meals'),
('Personal Care', 'Soap, shampoo, toothpaste, and hygiene products'),
('Household Items', 'Cleaning supplies and basic household needs'),
('Condiments', 'Soy sauce, vinegar, cooking oil, and spices'),
('Dairy Products', 'Milk, cheese, and dairy items'),
('Frozen Foods', 'Ice cream, frozen vegetables, and frozen meals'),
('School Supplies', 'Pens, notebooks, and basic school materials');

-- Insert sample products
INSERT INTO products (name, description, net_weight, price, stock_quantity, category_id, barcode) VALUES
-- Snacks
('Piattos Cheese', 'Potato chips with cheese flavor', '85g', 25.00, 50, (SELECT id FROM categories WHERE name = 'Snacks'), '4800016644801'),
('Skyflakes Crackers', 'Plain salted crackers', '250g', 35.00, 30, (SELECT id FROM categories WHERE name = 'Snacks'), '4800016644802'),
('Nova Multigrain', 'Spicy multigrain snack', '78g', 20.00, 40, (SELECT id FROM categories WHERE name = 'Snacks'), '4800016644803'),
('Ricoa Flat Tops', 'Chocolate wafer', '10pcs', 15.00, 60, (SELECT id FROM categories WHERE name = 'Snacks'), '4800016644804'),

-- Beverages
('Coca-Cola', 'Classic cola soft drink', '330ml', 18.00, 100, (SELECT id FROM categories WHERE name = 'Beverages'), '4800016644805'),
('Royal Tru-Orange', 'Orange flavored drink', '240ml', 12.00, 80, (SELECT id FROM categories WHERE name = 'Beverages'), '4800016644806'),
('C2 Green Tea', 'Green tea drink', '230ml', 15.00, 70, (SELECT id FROM categories WHERE name = 'Beverages'), '4800016644807'),
('Absolute Distilled Water', 'Purified drinking water', '500ml', 8.00, 120, (SELECT id FROM categories WHERE name = 'Beverages'), '4800016644808'),

-- Canned Goods
('Century Tuna Flakes', 'Tuna flakes in oil', '180g', 28.00, 45, (SELECT id FROM categories WHERE name = 'Canned Goods'), '4800016644809'),
('Argentina Corned Beef', 'Premium corned beef', '175g', 45.00, 25, (SELECT id FROM categories WHERE name = 'Canned Goods'), '4800016644810'),
('Ligo Sardines', 'Sardines in tomato sauce', '155g', 22.00, 35, (SELECT id FROM categories WHERE name = 'Canned Goods'), '4800016644811'),
('Del Monte Sweet Corn', 'Whole kernel corn', '432g', 38.00, 20, (SELECT id FROM categories WHERE name = 'Canned Goods'), '4800016644812'),

-- Instant Foods
('Lucky Me Pancit Canton', 'Instant stir-fry noodles', '60g', 12.00, 100, (SELECT id FROM categories WHERE name = 'Instant Foods'), '4800016644813'),
('Nescafe 3-in-1', 'Instant coffee mix', '20g', 8.00, 150, (SELECT id FROM categories WHERE name = 'Instant Foods'), '4800016644814'),
('Maggi Magic Sarap', 'All-in-one seasoning', '8g', 5.00, 200, (SELECT id FROM categories WHERE name = 'Instant Foods'), '4800016644815'),
('Nissin Cup Noodles', 'Instant cup noodles', '60g', 18.00, 80, (SELECT id FROM categories WHERE name = 'Instant Foods'), '4800016644816'),

-- Personal Care
('Safeguard Soap', 'Antibacterial soap bar', '130g', 25.00, 40, (SELECT id FROM categories WHERE name = 'Personal Care'), '4800016644817'),
('Colgate Toothpaste', 'Fluoride toothpaste', '100ml', 35.00, 30, (SELECT id FROM categories WHERE name = 'Personal Care'), '4800016644818'),
('Head & Shoulders Shampoo', 'Anti-dandruff shampoo', '12ml', 8.00, 100, (SELECT id FROM categories WHERE name = 'Personal Care'), '4800016644819'),
('Close-Up Toothbrush', 'Medium bristle toothbrush', '1pc', 15.00, 25, (SELECT id FROM categories WHERE name = 'Personal Care'), '4800016644820');

-- Insert sample customers
INSERT INTO customers (first_name, family_name, phone, address) VALUES
('Maria', 'Santos', '09171234567', 'Block 1 Lot 5, Barangay San Jose'),
('Juan', 'Dela Cruz', '09281234567', 'Block 2 Lot 10, Barangay San Jose'),
('Ana', 'Reyes', '09391234567', 'Block 1 Lot 15, Barangay San Jose'),
('Pedro', 'Garcia', '09451234567', 'Block 3 Lot 8, Barangay San Jose'),
('Rosa', 'Martinez', '09561234567', 'Block 2 Lot 20, Barangay San Jose'),
('Carlos', 'Lopez', '09671234567', 'Block 4 Lot 3, Barangay San Jose'),
('Elena', 'Gonzales', '09781234567', 'Block 1 Lot 25, Barangay San Jose'),
('Miguel', 'Torres', '09891234567', 'Block 3 Lot 12, Barangay San Jose'),
('Carmen', 'Flores', '09901234567', 'Block 2 Lot 18, Barangay San Jose'),
('Roberto', 'Morales', '09011234567', 'Block 4 Lot 7, Barangay San Jose');

-- Insert sample debt transactions
-- Note: In a real application, these would be created through the application interface
-- using the create_debt_transaction function

-- Maria Santos debts
INSERT INTO debt_transactions (customer_id, product_id, product_name, product_price, quantity, remaining_balance, debt_date) VALUES
((SELECT id FROM customers WHERE first_name = 'Maria' AND family_name = 'Santos'),
 (SELECT id FROM products WHERE name = 'Lucky Me Pancit Canton'),
 'Lucky Me Pancit Canton', 12.00, 5, 60.00, NOW() - INTERVAL '5 days'),
((SELECT id FROM customers WHERE first_name = 'Maria' AND family_name = 'Santos'),
 (SELECT id FROM products WHERE name = 'Coca-Cola'),
 'Coca-Cola', 18.00, 3, 54.00, NOW() - INTERVAL '3 days');

-- Juan Dela Cruz debts
INSERT INTO debt_transactions (customer_id, product_id, product_name, product_price, quantity, remaining_balance, debt_date) VALUES
((SELECT id FROM customers WHERE first_name = 'Juan' AND family_name = 'Dela Cruz'),
 (SELECT id FROM products WHERE name = 'Century Tuna Flakes'),
 'Century Tuna Flakes', 28.00, 2, 56.00, NOW() - INTERVAL '7 days'),
((SELECT id FROM customers WHERE first_name = 'Juan' AND family_name = 'Dela Cruz'),
 (SELECT id FROM products WHERE name = 'Skyflakes Crackers'),
 'Skyflakes Crackers', 35.00, 1, 35.00, NOW() - INTERVAL '2 days');

-- Ana Reyes debts
INSERT INTO debt_transactions (customer_id, product_id, product_name, product_price, quantity, remaining_balance, debt_date) VALUES
((SELECT id FROM customers WHERE first_name = 'Ana' AND family_name = 'Reyes'),
 (SELECT id FROM products WHERE name = 'Nescafe 3-in-1'),
 'Nescafe 3-in-1', 8.00, 10, 80.00, NOW() - INTERVAL '4 days'),
((SELECT id FROM customers WHERE first_name = 'Ana' AND family_name = 'Reyes'),
 (SELECT id FROM products WHERE name = 'Safeguard Soap'),
 'Safeguard Soap', 25.00, 2, 50.00, NOW() - INTERVAL '1 day');

-- Pedro Garcia debts
INSERT INTO debt_transactions (customer_id, product_id, product_name, product_price, quantity, remaining_balance, debt_date) VALUES
((SELECT id FROM customers WHERE first_name = 'Pedro' AND family_name = 'Garcia'),
 (SELECT id FROM products WHERE name = 'Argentina Corned Beef'),
 'Argentina Corned Beef', 45.00, 1, 45.00, NOW() - INTERVAL '6 days'),
((SELECT id FROM customers WHERE first_name = 'Pedro' AND family_name = 'Garcia'),
 (SELECT id FROM products WHERE name = 'Royal Tru-Orange'),
 'Royal Tru-Orange', 12.00, 4, 48.00, NOW() - INTERVAL '1 day');

-- Insert sample payments
INSERT INTO payments (customer_id, amount_paid, payment_method, notes, payment_date) VALUES
((SELECT id FROM customers WHERE first_name = 'Maria' AND family_name = 'Santos'),
 50.00, 'cash', 'Partial payment', NOW() - INTERVAL '2 days'),
((SELECT id FROM customers WHERE first_name = 'Juan' AND family_name = 'Dela Cruz'),
 30.00, 'cash', 'Partial payment', NOW() - INTERVAL '1 day'),
((SELECT id FROM customers WHERE first_name = 'Ana' AND family_name = 'Reyes'),
 25.00, 'gcash', 'GCash payment', NOW() - INTERVAL '3 hours');

-- Update remaining balances after payments (this would normally be handled by the process_payment function)
-- Maria Santos: Paid 50.00 towards 114.00 total debt
UPDATE debt_transactions 
SET remaining_balance = 10.00 
WHERE customer_id = (SELECT id FROM customers WHERE first_name = 'Maria' AND family_name = 'Santos')
AND product_name = 'Lucky Me Pancit Canton';

-- Juan Dela Cruz: Paid 30.00 towards 91.00 total debt  
UPDATE debt_transactions 
SET remaining_balance = 26.00 
WHERE customer_id = (SELECT id FROM customers WHERE first_name = 'Juan' AND family_name = 'Dela Cruz')
AND product_name = 'Century Tuna Flakes';

-- Ana Reyes: Paid 25.00 towards 130.00 total debt
UPDATE debt_transactions 
SET remaining_balance = 55.00 
WHERE customer_id = (SELECT id FROM customers WHERE first_name = 'Ana' AND family_name = 'Reyes')
AND product_name = 'Nescafe 3-in-1';
