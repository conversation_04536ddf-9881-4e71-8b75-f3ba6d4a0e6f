'use client'

import { useSearchParams } from 'next/navigation'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { DebtForm } from '@/components/forms/DebtForm'

function AddDebtContent() {
  const searchParams = useSearchParams()
  const customerId = searchParams.get('customer_id')

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Record New Debt</h1>
        <p className="text-gray-600 mt-2">Record a customer purchase on credit</p>
      </div>

      <DebtForm preselectedCustomerId={customerId || undefined} />
    </div>
  )
}

export default function AddDebtPage() {
  return (
    <ProtectedRoute requiredPermission="create_debt">
      <AddDebtContent />
    </ProtectedRoute>
  )
}
