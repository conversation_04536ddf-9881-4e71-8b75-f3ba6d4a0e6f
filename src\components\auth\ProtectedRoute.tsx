'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/components/providers/AuthProvider'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: string | string[]
  requiredPermission?: string
  fallback?: React.ReactNode
}

export function ProtectedRoute({
  children,
  requiredRole,
  requiredPermission,
  fallback
}: ProtectedRouteProps) {
  const { user, loading } = useAuth()
  const { hasRole, canPerformAction } = usePermissions()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      // Redirect to login if not authenticated
      if (!user) {
        router.push('/auth/login')
        return
      }

      // Check if user has required role
      if (requiredRole && !hasRole(requiredRole)) {
        router.push('/unauthorized')
        return
      }

      // Check if user has required permission
      if (requiredPermission && !canPerformAction(requiredPermission)) {
        router.push('/unauthorized')
        return
      }
    }
  }, [user, loading, router, requiredRole, requiredPermission, hasRole, canPerformAction])

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Show fallback or nothing if not authenticated
  if (!user) {
    return fallback || null
  }

  // Check role requirements
  if (requiredRole && !hasRole(requiredRole)) {
    return fallback || null
  }

  // Check permission requirements
  if (requiredPermission && !canPerformAction(requiredPermission)) {
    return fallback || null
  }

  // Render children if all checks pass
  return <>{children}</>
}

// Higher-order component for protecting pages
export function withProtectedRoute<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: {
    requiredRole?: string | string[]
    requiredPermission?: string
  } = {}
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute
        requiredRole={options.requiredRole}
        requiredPermission={options.requiredPermission}
      >
        <WrappedComponent {...props} />
      </ProtectedRoute>
    )
  }
}

// Component for role-based rendering
interface RoleGuardProps {
  children: React.ReactNode
  requiredRole?: string | string[]
  requiredPermission?: string
  fallback?: React.ReactNode
}

export function RoleGuard({
  children,
  requiredRole,
  requiredPermission,
  fallback = null
}: RoleGuardProps) {
  const { hasRole, canPerformAction } = usePermissions()

  // Check role requirements
  if (requiredRole && !hasRole(requiredRole)) {
    return <>{fallback}</>
  }

  // Check permission requirements
  if (requiredPermission && !canPerformAction(requiredPermission)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Hook for conditional rendering based on permissions
export function useConditionalRender() {
  const { hasRole, canPerformAction } = usePermissions()

  const renderIf = (
    condition: boolean | (() => boolean),
    component: React.ReactNode,
    fallback: React.ReactNode = null
  ) => {
    const shouldRender = typeof condition === 'function' ? condition() : condition
    return shouldRender ? component : fallback
  }

  const renderIfRole = (
    requiredRole: string | string[],
    component: React.ReactNode,
    fallback: React.ReactNode = null
  ) => {
    return renderIf(() => hasRole(requiredRole), component, fallback)
  }

  const renderIfPermission = (
    permission: string,
    component: React.ReactNode,
    fallback: React.ReactNode = null
  ) => {
    return renderIf(() => canPerformAction(permission), component, fallback)
  }

  return {
    renderIf,
    renderIfRole,
    renderIfPermission
  }
}
